from flask import Blueprint, session, request, jsonify
from app.config import get_db_connection
from datetime import datetime, timedelta
import pymysql

admin_dashboard_bp = Blueprint('admin_dashboard', __name__)

@admin_dashboard_bp.route('/get_dashboard_stats', methods=['GET'])
def get_dashboard_stats():
    """
    获取管理员首页统计数据
    请求参数:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
    返回:
        各类统计数据
    """
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "无权限访问"})
    
    try:
        # 获取日期参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        
        # 如果没有指定日期，默认查询最近30天
        if not start_date or not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        # 建立数据库连接
        connection = get_db_connection()
        cursor = connection.cursor(pymysql.cursors.DictCursor)
        
        # 构建日期过滤条件
        date_filter = f"DATE(created_at) BETWEEN '{start_date}' AND '{end_date}'"
        
        # 1. 用户统计
        user_stats = get_user_statistics(cursor, date_filter, start_date, end_date)
        
        # 2. 样书统计
        book_stats = get_book_statistics(cursor, date_filter, start_date, end_date)
        
        # 3. 订单统计
        order_stats = get_order_statistics(cursor, date_filter, start_date, end_date)
        
        # 4. 推广统计
        promotion_stats = get_promotion_statistics(cursor, date_filter, start_date, end_date)
        
        # 5. 书展统计
        exhibition_stats = get_exhibition_statistics(cursor, date_filter, start_date, end_date)
        
        # 6. 系统活跃度统计
        activity_stats = get_activity_statistics(cursor, date_filter, start_date, end_date)
        
        # 7. 财务统计
        financial_stats = get_financial_statistics(cursor, date_filter, start_date, end_date)
        
        # 8. 地域分布统计
        regional_stats = get_regional_statistics(cursor)
        
        cursor.close()
        connection.close()
        
        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": {
                "date_range": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "user_stats": user_stats,
                "book_stats": book_stats,
                "order_stats": order_stats,
                "promotion_stats": promotion_stats,
                "exhibition_stats": exhibition_stats,
                "activity_stats": activity_stats,
                "financial_stats": financial_stats,
                "regional_stats": regional_stats
            }
        })
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取统计数据失败: {str(e)}"})

def get_user_statistics(cursor, date_filter, start_date, end_date):
    """获取用户统计数据"""
    stats = {}
    
    # 总用户数
    cursor.execute("SELECT COUNT(*) as total FROM users")
    stats['total_users'] = cursor.fetchone()['total']
    
    # 新增用户数（指定时间段）
    cursor.execute(f"SELECT COUNT(*) as new_users FROM users WHERE {date_filter}")
    stats['new_users'] = cursor.fetchone()['new_users']
    
    # 各角色用户数
    cursor.execute("""
        SELECT role, COUNT(*) as count 
        FROM users 
        GROUP BY role
    """)
    role_counts = cursor.fetchall()
    stats['role_distribution'] = {item['role']: item['count'] for item in role_counts}
    
    # 活跃用户数（最近7天有登录记录的用户）
    cursor.execute("""
        SELECT COUNT(DISTINCT user_id) as active_users 
        FROM audit_logs 
        WHERE action_type = 'login' 
        AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    """)
    result = cursor.fetchone()
    stats['active_users'] = result['active_users'] if result else 0
    
    # 用户增长趋势（最近7天）
    cursor.execute("""
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date
    """)
    growth_trend = cursor.fetchall()
    stats['growth_trend'] = [{'date': item['date'].strftime('%Y-%m-%d'), 'count': item['count']} for item in growth_trend]
    
    return stats

def get_book_statistics(cursor, date_filter, start_date, end_date):
    """获取样书统计数据"""
    stats = {}
    
    # 总样书数
    cursor.execute("SELECT COUNT(*) as total FROM sample_books")
    stats['total_books'] = cursor.fetchone()['total']
    
    # 新增样书数
    cursor.execute(f"SELECT COUNT(*) as new_books FROM sample_books WHERE {date_filter}")
    stats['new_books'] = cursor.fetchone()['new_books']
    
    # 各状态样书数
    cursor.execute("""
        SELECT status, COUNT(*) as count 
        FROM sample_books 
        GROUP BY status
    """)
    status_counts = cursor.fetchall()
    stats['status_distribution'] = {item['status']: item['count'] for item in status_counts}
    
    # 各出版社样书数量排行（前10）
    cursor.execute("""
        SELECT pc.name as publisher_name, COUNT(sb.id) as book_count
        FROM sample_books sb
        JOIN users u ON sb.publisher_id = u.user_id
        JOIN publisher_companies pc ON u.publisher_company_id = pc.id
        GROUP BY pc.id, pc.name
        ORDER BY book_count DESC
        LIMIT 10
    """)
    publisher_ranking = cursor.fetchall()
    stats['publisher_ranking'] = publisher_ranking
    
    # 样书类型分布
    cursor.execute("""
        SELECT book_type, COUNT(*) as count 
        FROM sample_books 
        WHERE book_type IS NOT NULL AND book_type != ''
        GROUP BY book_type
        ORDER BY count DESC
        LIMIT 10
    """)
    type_distribution = cursor.fetchall()
    stats['type_distribution'] = type_distribution
    
    return stats

def get_order_statistics(cursor, date_filter, start_date, end_date):
    """获取订单统计数据"""
    stats = {}
    
    # 总订单数
    cursor.execute("SELECT COUNT(*) as total FROM order_items")
    stats['total_orders'] = cursor.fetchone()['total']
    
    # 新增订单数
    cursor.execute(f"SELECT COUNT(*) as new_orders FROM order_items WHERE {date_filter}")
    stats['new_orders'] = cursor.fetchone()['new_orders']
    
    # 订单状态分布
    cursor.execute("""
        SELECT reconciliation_status, COUNT(*) as count 
        FROM order_items 
        GROUP BY reconciliation_status
    """)
    status_counts = cursor.fetchall()
    stats['status_distribution'] = {item['reconciliation_status']: item['count'] for item in status_counts}
    
    # 总发货量和退货量
    cursor.execute("""
        SELECT 
            SUM(shipped_quantity) as total_shipped,
            SUM(returned_quantity) as total_returned
        FROM order_items
    """)
    quantity_stats = cursor.fetchone()
    stats['total_shipped'] = quantity_stats['total_shipped'] or 0
    stats['total_returned'] = quantity_stats['total_returned'] or 0
    
    # 订单金额统计
    cursor.execute("""
        SELECT 
            SUM(shipped_quantity * unit_price) as total_amount,
            AVG(shipped_quantity * unit_price) as avg_amount
        FROM order_items
        WHERE shipped_quantity > 0
    """)
    amount_stats = cursor.fetchone()
    stats['total_amount'] = float(amount_stats['total_amount'] or 0)
    stats['avg_amount'] = float(amount_stats['avg_amount'] or 0)
    
    return stats

def get_promotion_statistics(cursor, date_filter, start_date, end_date):
    """获取推广统计数据"""
    stats = {}
    
    # 总推广报备数
    cursor.execute("SELECT COUNT(*) as total FROM promotion_reports")
    stats['total_reports'] = cursor.fetchone()['total']
    
    # 新增推广报备数
    cursor.execute(f"SELECT COUNT(*) as new_reports FROM promotion_reports WHERE {date_filter}")
    stats['new_reports'] = cursor.fetchone()['new_reports']
    
    # 推广状态分布
    cursor.execute("""
        SELECT status, COUNT(*) as count 
        FROM promotion_reports 
        GROUP BY status
    """)
    status_counts = cursor.fetchall()
    stats['status_distribution'] = {item['status']: item['count'] for item in status_counts}
    
    # 推广成功率
    cursor.execute("""
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved
        FROM promotion_reports
    """)
    success_stats = cursor.fetchone()
    total = success_stats['total']
    approved = success_stats['approved']
    stats['success_rate'] = round((approved / total * 100), 2) if total > 0 else 0
    
    return stats

def get_exhibition_statistics(cursor, date_filter, start_date, end_date):
    """获取书展统计数据"""
    stats = {}
    
    # 总书展数
    cursor.execute("SELECT COUNT(*) as total FROM book_exhibitions")
    stats['total_exhibitions'] = cursor.fetchone()['total']
    
    # 新增书展数
    cursor.execute(f"SELECT COUNT(*) as new_exhibitions FROM book_exhibitions WHERE {date_filter}")
    stats['new_exhibitions'] = cursor.fetchone()['new_exhibitions']
    
    # 书展状态分布
    cursor.execute("""
        SELECT status, COUNT(*) as count 
        FROM book_exhibitions 
        GROUP BY status
    """)
    status_counts = cursor.fetchall()
    stats['status_distribution'] = {item['status']: item['count'] for item in status_counts}
    
    # 总报名数和参展人数
    cursor.execute("""
        SELECT 
            COUNT(*) as total_registrations,
            SUM(CASE WHEN status = 'registered' THEN 1 ELSE 0 END) as active_registrations
        FROM exhibition_registrations
    """)
    reg_stats = cursor.fetchone()
    stats['total_registrations'] = reg_stats['total_registrations']
    stats['active_registrations'] = reg_stats['active_registrations']
    
    cursor.execute("""
        SELECT COUNT(*) as total_participants
        FROM exhibition_participants ep
        JOIN exhibition_registrations er ON ep.registration_id = er.id
        WHERE er.status = 'registered'
    """)
    stats['total_participants'] = cursor.fetchone()['total_participants']
    
    return stats

def get_activity_statistics(cursor, date_filter, start_date, end_date):
    """获取系统活跃度统计"""
    stats = {}
    
    # 总操作日志数
    cursor.execute("SELECT COUNT(*) as total FROM audit_logs")
    stats['total_logs'] = cursor.fetchone()['total']
    
    # 指定时间段操作数
    cursor.execute(f"SELECT COUNT(*) as period_logs FROM audit_logs WHERE {date_filter}")
    stats['period_logs'] = cursor.fetchone()['period_logs']
    
    # 操作类型分布（最近7天）
    cursor.execute("""
        SELECT action_type, COUNT(*) as count 
        FROM audit_logs 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY action_type
        ORDER BY count DESC
        LIMIT 10
    """)
    action_distribution = cursor.fetchall()
    stats['action_distribution'] = action_distribution
    
    # 每日活跃度（最近7天）
    cursor.execute("""
        SELECT DATE(created_at) as date, COUNT(DISTINCT user_id) as active_users
        FROM audit_logs 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date
    """)
    daily_activity = cursor.fetchall()
    stats['daily_activity'] = [{'date': item['date'].strftime('%Y-%m-%d'), 'active_users': item['active_users']} for item in daily_activity]
    
    return stats

def get_financial_statistics(cursor, date_filter, start_date, end_date):
    """获取财务统计数据"""
    stats = {}
    
    # 总交易金额
    cursor.execute("""
        SELECT SUM(shipped_quantity * unit_price) as total_revenue
        FROM order_items
        WHERE shipped_quantity > 0
    """)
    stats['total_revenue'] = float(cursor.fetchone()['total_revenue'] or 0)
    
    # 指定时间段交易金额
    cursor.execute(f"""
        SELECT SUM(shipped_quantity * unit_price) as period_revenue
        FROM order_items
        WHERE shipped_quantity > 0 AND {date_filter}
    """)
    stats['period_revenue'] = float(cursor.fetchone()['period_revenue'] or 0)
    
    # 支付状态分布
    cursor.execute("""
        SELECT payment_status, COUNT(*) as count, SUM(shipped_quantity * unit_price) as amount
        FROM order_items
        WHERE shipped_quantity > 0
        GROUP BY payment_status
    """)
    payment_stats = cursor.fetchall()
    stats['payment_distribution'] = [
        {
            'status': '已支付' if item['payment_status'] == 1 else '未支付',
            'count': item['count'],
            'amount': float(item['amount'] or 0)
        } for item in payment_stats
    ]
    
    return stats

def get_regional_statistics(cursor):
    """获取地域分布统计"""
    stats = {}
    
    # 学校地域分布（按省份）
    cursor.execute("""
        SELECT province, COUNT(*) as school_count
        FROM school_addresses sa
        JOIN schools s ON sa.school_id = s.id
        WHERE sa.is_main = 1
        GROUP BY province
        ORDER BY school_count DESC
        LIMIT 10
    """)
    school_distribution = cursor.fetchall()
    stats['school_distribution'] = school_distribution
    
    # 出版社地域分布（按省份）
    cursor.execute("""
        SELECT province, COUNT(*) as publisher_count
        FROM publisher_addresses pa
        JOIN publisher_companies pc ON pa.publisher_company_id = pc.id
        WHERE pa.is_main = 1
        GROUP BY province
        ORDER BY publisher_count DESC
        LIMIT 10
    """)
    publisher_distribution = cursor.fetchall()
    stats['publisher_distribution'] = publisher_distribution
    
    return stats
