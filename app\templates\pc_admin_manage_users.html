<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script src="/static/jquery.js"></script>
    <style>
        /* 按钮样式 - 基于设计规范 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }
        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transition: all 0.3s ease;
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }
        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
            transition: all 0.3s ease;
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            transition: all 0.3s ease;
        }
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
        }

        /* 状态标签 */
        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            display: inline-flex;
            align-items: center;
        }
        .role-admin {
            background: #fee2e2;
            color: #b91c1c;
        }
        .role-teacher {
            background: #d1fae5;
            color: #065f46;
        }
        .role-publisher {
            background: #dbeafe;
            color: #1d4ed8;
        }
        .role-dealer {
            background: #ede9fe;
            color: #6d28d9;
        }

        /* 标签页 */
        .tab-active {
            background: white;
            color: #2563eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            font-weight: 500;
        }
        .tab-inactive {
            color: #64748b;
            background: transparent;
        }
        .tab-inactive:hover {
            color: #334155;
            background: rgba(248, 250, 252, 0.8);
        }

        /* 卡片组件 */
        .user-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid #e2e8f0;
            background: white/80;
            backdrop-filter: blur(8px);
        }
        .user-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border-color: #cbd5e1;
        }

        /* 模态框样式 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 消息通知动画 */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        .message-slide-in {
            animation: slideInRight 0.3s ease-out;
        }

        /* 计数器标签 */
        .count-badge {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
            font-size: 10px;
            font-weight: 600;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
        }

        /* 搜索框组件 */
        .search-container input {
            transition: all 0.3s ease;
        }
        .search-container input:focus {
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* 搜索下拉框样式 */
        .search-dropdown {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            font-size: 14px;
            background: white;
            transition: all 0.2s ease;
        }

        .search-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .search-dropdown-menu {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #d1d5db;
            border-radius: 12px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 4px;
            display: none;
        }

        .search-dropdown-menu.show {
            display: block;
        }

        .search-dropdown-item {
            padding: 12px 16px;
            cursor: pointer;
            border-bottom: 1px solid #f1f5f9;
            transition: background-color 0.15s ease;
        }

        .search-dropdown-item:hover {
            background-color: #f8fafc;
        }

        .search-dropdown-item:last-child {
            border-bottom: none;
        }

        .search-dropdown-empty {
            padding: 12px 16px;
            color: #9ca3af;
            text-align: center;
            font-style: italic;
        }

        .search-dropdown-hint {
            padding: 8px 16px;
            color: #6b7280;
            font-size: 12px;
            background-color: #f9fafb;
            border-bottom: 1px solid #e5e7eb;
        }

        .search-dropdown-more {
            padding: 8px 16px;
            color: #3b82f6;
            font-size: 12px;
            cursor: pointer;
            background-color: #f8fafc;
            border-top: 1px solid #e5e7eb;
            text-align: center;
        }

        .search-dropdown-more:hover {
            background-color: #e2e8f0;
        }

        .search-dropdown-selected {
            background-color: #dbeafe !important;
            color: #1d4ed8 !important;
        }

        /* 模态框样式 - 按照设计规范 */
        .modal-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
            z-index: 50;
        }

        /* 自定义滚动条样式 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 用户详情卡片样式 */
        .user-detail-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .user-detail-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }

        /* 权限状态图标动画 */
        .permission-icon {
            transition: transform 0.2s ease;
        }

        .permission-icon:hover {
            transform: scale(1.1);
        }

    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen">
    <!-- 消息提示容器 -->
    <div id="messageContainer" class="fixed top-6 right-6 z-[9999] space-y-3 max-w-sm"></div>

    <div class="container mx-auto px-6 py-8">
        <!-- 标签页切换 -->
        <div class="flex bg-slate-100 rounded-xl p-1 mb-8 max-w-4xl mx-auto">
            <button id="allTab" class="flex-1 py-3 px-6 text-center tab-active rounded-lg transition-all duration-200 min-w-0">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-users text-sm"></i>
                    <span class="font-medium whitespace-nowrap">全部用户</span>
                    <span id="allCount" class="count-badge">0</span>
                </div>
            </button>
            <button id="adminTab" class="flex-1 py-3 px-6 text-center tab-inactive rounded-lg transition-all duration-200 min-w-0">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-user-shield text-sm"></i>
                    <span class="font-medium whitespace-nowrap">管理员</span>
                    <span id="adminCount" class="count-badge bg-red-500">0</span>
                </div>
            </button>
            <button id="teacherTab" class="flex-1 py-3 px-6 text-center tab-inactive rounded-lg transition-all duration-200 min-w-0">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-chalkboard-teacher text-sm"></i>
                    <span class="font-medium whitespace-nowrap">教师</span>
                    <span id="teacherCount" class="count-badge bg-green-500">0</span>
                </div>
            </button>
            <button id="publisherTab" class="flex-1 py-3 px-6 text-center tab-inactive rounded-lg transition-all duration-200 min-w-0">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-building text-sm"></i>
                    <span class="font-medium whitespace-nowrap">出版社</span>
                    <span id="publisherCount" class="count-badge bg-blue-500">0</span>
                </div>
            </button>
            <button id="dealerTab" class="flex-1 py-3 px-6 text-center tab-inactive rounded-lg transition-all duration-200 min-w-0">
                <div class="flex items-center justify-center space-x-2">
                    <i class="fas fa-store text-sm"></i>
                    <span class="font-medium whitespace-nowrap">经销商</span>
                    <span id="dealerCount" class="count-badge bg-purple-500">0</span>
                </div>
            </button>
        </div>
        
        <!-- 搜索和筛选区域 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-sm border border-slate-200 p-6 mb-8">
            <div class="flex flex-wrap gap-4 items-center">
                <!-- 搜索框 -->
                <div class="search-container flex-1 min-w-[300px]">
                    <div class="relative">
                        <input type="text" id="searchInput"
                               placeholder="搜索用户名、姓名、手机号..."
                               class="w-full h-12 pl-4 pr-12 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <button id="searchBtn" class="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center">
                            <i class="fas fa-search text-sm"></i>
                        </button>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center gap-3">
                    <button id="refreshBtn" class="h-12 px-4 bg-slate-100 hover:bg-slate-200 text-slate-700 rounded-xl flex items-center space-x-2 transition-all">
                        <i class="fas fa-sync-alt"></i>
                        <span>刷新</span>
                    </button>

                    <button id="importUsersBtn" class="h-12 px-4 btn-success text-white rounded-xl flex items-center space-x-2">
                        <i class="fas fa-file-import"></i>
                        <span>批量导入</span>
                    </button>

                    <button id="addUserBtn" class="h-12 px-4 btn-primary text-white rounded-xl flex items-center space-x-2">
                        <i class="fas fa-user-plus"></i>
                        <span>添加用户</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 用户列表容器 -->
        <div class="bg-white rounded-2xl shadow-sm border border-slate-200 overflow-hidden">
            <!-- 表格头部 -->
            <div class="bg-slate-50 px-6 py-4 border-b border-slate-200">
                <div class="grid grid-cols-12 gap-4 text-sm font-medium text-slate-600">
                    <div class="col-span-2">
                        <button class="flex items-center space-x-1 hover:text-slate-800 transition-colors sort-header" data-field="name">
                            <span>用户信息</span>
                            <div class="flex flex-col">
                                <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"></i>
                                <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"></i>
                            </div>
                        </button>
                    </div>
                    <div class="col-span-1">
                        <button class="flex items-center space-x-1 hover:text-slate-800 transition-colors sort-header" data-field="role">
                            <span>角色</span>
                            <div class="flex flex-col">
                                <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"></i>
                                <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"></i>
                            </div>
                        </button>
                    </div>
                    <div class="col-span-2">
                        <button class="flex items-center space-x-1 hover:text-slate-800 transition-colors sort-header" data-field="phone">
                            <span>联系方式</span>
                            <div class="flex flex-col">
                                <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"></i>
                                <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"></i>
                            </div>
                        </button>
                    </div>
                    <div class="col-span-3">
                        <button class="flex items-center space-x-1 hover:text-slate-800 transition-colors sort-header" data-field="organization">
                            <span>所属机构</span>
                            <div class="flex flex-col">
                                <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"></i>
                                <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"></i>
                            </div>
                        </button>
                    </div>
                    <div class="col-span-2">
                        <button class="flex items-center space-x-1 hover:text-slate-800 transition-colors sort-header" data-field="created_at">
                            <span>注册时间</span>
                            <div class="flex flex-col">
                                <i class="fas fa-caret-up text-xs sort-icon sort-asc opacity-30"></i>
                                <i class="fas fa-caret-down text-xs sort-icon sort-desc opacity-30 -mt-1"></i>
                            </div>
                        </button>
                    </div>
                    <div class="col-span-2 text-right">操作</div>
                </div>
            </div>
            <!-- 用户列表 -->
            <div id="usersContainer">
                <!-- 这里将通过JS动态加载用户列表 -->
            </div>
        </div>

        <!-- 分页控件 -->
        <div class="flex justify-between items-center mt-8 bg-white/80 backdrop-blur-sm rounded-xl shadow-sm border border-slate-200 p-4">
            <!-- 信息显示区域 -->
            <div class="flex items-center">
                <p class="text-sm text-gray-700 mr-4">
                    第 <span id="currentPageDisplay" class="font-medium">1</span> 页，
                    共 <span id="totalPagesDisplay" class="font-medium">1</span> 页，
                    共 <span id="totalCountDisplay" class="font-medium">0</span> 条
                </p>
            </div>

            <!-- 分页按钮区域 -->
            <div class="flex gap-1">
                <!-- 首页按钮 -->
                <button id="firstPageBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="sr-only">首页</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414zm-6 0a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 011.414 1.414L5.414 10l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
                    </svg>
                </button>

                <!-- 上一页按钮 -->
                <button id="prevPageBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    上一页
                </button>

                <!-- 页码按钮容器 -->
                <div id="pageNumbers" class="flex gap-1">
                    <!-- 页码将通过JavaScript动态生成 -->
                </div>

                <!-- 下一页按钮 -->
                <button id="nextPageBtn" class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    下一页
                </button>

                <!-- 末页按钮 -->
                <button id="lastPageBtn" class="relative inline-flex items-center px-2 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">
                    <span class="sr-only">末页</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        <path fill-rule="evenodd" d="M4.293 15.707a1 1 0 010-1.414L8.586 10 4.293 5.707a1 1 0 011.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 确认模态框 -->
    <div id="confirmModalContainer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] hidden">
        <div class="bg-white rounded-2xl shadow-xl max-w-md w-full mx-4">
            <div class="p-6 border-b border-slate-200">
                <h3 id="confirmModalTitle" class="text-xl font-semibold text-slate-800">确认操作</h3>
            </div>
            <div class="p-6">
                <p id="confirmModalContent" class="text-slate-600">确定要执行此操作吗？</p>
            </div>
            <div class="px-6 py-4 bg-slate-50 flex justify-end space-x-3 rounded-b-2xl">
                <button type="button" class="px-4 py-2 bg-slate-200 text-slate-700 rounded-lg hover:bg-slate-300 transition-colors" onclick="$('#confirmModalContainer').addClass('hidden')">取消</button>
                <button id="confirmModalYesBtn" type="button" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors">确定</button>
            </div>
        </div>
    </div>

    <!-- 错误提示模态框 -->
    <div id="errorModalContainer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] hidden">
        <div class="bg-white rounded-2xl shadow-xl max-w-md w-full mx-4">
            <div class="p-6 border-b border-slate-200">
                <h3 class="text-xl font-semibold text-red-600">错误提示</h3>
            </div>
            <div class="p-6">
                <p id="errorModalContent" class="text-slate-600">操作失败，请稍后重试。</p>
            </div>
            <div class="px-6 py-4 bg-slate-50 text-right rounded-b-2xl">
                <button type="button" class="px-4 py-2 bg-slate-200 text-slate-700 rounded-lg hover:bg-slate-300 transition-colors" onclick="$('#errorModalContainer').addClass('hidden')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 成功提示模态框 -->
    <div id="successModalContainer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] hidden">
        <div class="bg-white rounded-2xl shadow-xl max-w-md w-full mx-4">
            <div class="p-6 border-b border-slate-200">
                <h3 class="text-xl font-semibold text-green-600">操作成功</h3>
            </div>
            <div class="p-6">
                <p id="successModalContent" class="text-slate-600">操作已成功完成。</p>
            </div>
            <div class="px-6 py-4 bg-slate-50 text-right rounded-b-2xl">
                <button type="button" class="px-4 py-2 bg-slate-200 text-slate-700 rounded-lg hover:bg-slate-300 transition-colors" onclick="$('#successModalContainer').addClass('hidden')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 用户详情模态框 - 按照设计规范优化 -->
    <div id="userDetailModalContainer" class="fixed inset-0 z-50 hidden">
        <div class="modal-overlay flex items-center justify-center p-4">
            <div class="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
                <div class="flex items-center justify-between p-6 border-b border-slate-200">
                    <h3 id="userDetailModalTitle" class="text-xl font-semibold text-slate-800 flex items-center space-x-2">
                        <i class="fas fa-user-circle text-blue-500"></i>
                        <span>用户详情</span>
                    </h3>
                    <button onclick="closeUserDetailModal()"
                            class="w-8 h-8 bg-slate-100 hover:bg-slate-200 text-slate-600 rounded-lg flex items-center justify-center transition-colors">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
                <div id="userDetailModalBody" class="p-6 overflow-y-auto max-h-[70vh] custom-scrollbar">
                    <div id="userDetailContent">
                        <!-- 用户详情内容将通过JavaScript动态加载 -->
                    </div>
                </div>
                <div class="px-6 py-4 bg-slate-50 border-t border-slate-200 flex justify-end space-x-3 rounded-b-2xl">
                    <button type="button" class="px-4 py-2 bg-slate-200 text-slate-700 rounded-lg hover:bg-slate-300 transition-colors" onclick="closeUserDetailModal()">
                        <i class="fas fa-times text-xs mr-1"></i>
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加/编辑用户模态框 -->
    <div id="userFormModalContainer" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] flex flex-col">
            <div class="p-6 flex justify-between items-center border-b border-slate-200">
                <h3 id="userFormTitle" class="text-xl font-semibold text-slate-800">添加用户</h3>
                <button type="button" class="text-slate-400 hover:text-slate-600 p-2 rounded-lg hover:bg-slate-100 transition-colors" onclick="$('#userFormModalContainer').addClass('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 overflow-y-auto">
                <form id="userForm" class="space-y-4">
                    <input type="hidden" id="userId" name="userId">
                    
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="username" class="block text-sm font-medium text-slate-700 mb-2">用户名 <span class="text-red-500">*</span></label>
                            <input type="text" id="username" name="username" class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <div class="form-group" id="passwordField">
                            <label for="password" class="block text-sm font-medium text-slate-700 mb-2">密码 <span class="text-red-500">*</span></label>
                            <input type="password" id="password" name="password" class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <p class="text-xs text-slate-500 mt-1">请输入密码</p>
                        </div>

                        <div class="form-group">
                            <label for="name" class="block text-sm font-medium text-slate-700 mb-2">姓名 <span class="text-red-500">*</span></label>
                            <input type="text" id="name" name="name" class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <div class="form-group">
                            <label for="phone" class="block text-sm font-medium text-slate-700 mb-2">电话 <span class="text-red-500">*</span></label>
                            <input type="text" id="phone" name="phone" class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <div class="form-group">
                            <label for="email" class="block text-sm font-medium text-slate-700 mb-2">邮箱 <span class="text-red-500">*</span></label>
                            <input type="email" id="email" name="email" required class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>

                        <div class="form-group col-span-2">
                            <label for="role" class="block text-sm font-medium text-slate-700 mb-2">角色 <span class="text-red-500">*</span></label>
                            <select id="role" name="role" class="w-full h-12 px-4 bg-white border border-slate-200 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" onchange="updateRoleFields()">
                                <option value="">请选择角色</option>
                                <option value="admin">管理员</option>
                                <option value="teacher">教师</option>
                                <option value="publisher">出版社</option>
                                <option value="dealer">经销商</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- 角色特定字段区域 -->
                    <div class="mt-8 pt-5 border-t border-gray-200">
                        <h4 class="text-lg font-medium text-gray-800 mb-4">角色专用信息</h4>
                        
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <!-- 学校相关字段（教师角色） -->
                            <div id="schoolField" class="hidden mb-4">
                                <label for="schoolFormSearch" class="block text-sm font-medium text-gray-700 mb-1">学校<span class="text-red-500">*</span></label>
                                <div id="schoolFormContainer" class="search-dropdown">
                                    <input type="text" id="schoolFormSearch" class="search-input" placeholder="请输入学校名称搜索..." autocomplete="off">
                                    <div id="schoolFormDropdown" class="search-dropdown-menu">
                                        <div class="search-dropdown-empty">请选择学校</div>
                                        <!-- 学校选项将通过JS动态加载 -->
                                    </div>
                                    <input type="hidden" id="schoolId" name="schoolId">
                                </div>
                            </div>
                            
                            
                            <!-- 出版社相关字段 -->
                            <div id="publisherField" class="hidden mb-4">
                                <label for="publisherFormSearch" class="block text-sm font-medium text-gray-700 mb-1">出版社<span class="text-red-500">*</span></label>
                                <div id="publisherFormContainer" class="search-dropdown">
                                    <input type="text" id="publisherFormSearch" class="search-input" placeholder="请输入出版社名称搜索..." autocomplete="off">
                                    <div id="publisherFormDropdown" class="search-dropdown-menu">
                                        <div class="search-dropdown-empty">请选择出版社</div>
                                        <!-- 出版社选项将通过JS动态加载 -->
                                    </div>
                                    <input type="hidden" id="publisherId" name="publisherId">
                                </div>
                            </div>
                            
                            <!-- 经销商相关字段 -->
                            <div id="dealerField" class="hidden mb-4">
                                <label for="dealerFormSearch" class="block text-sm font-medium text-gray-700 mb-1">经销商<span class="text-red-500">*</span></label>
                                <div id="dealerFormContainer" class="search-dropdown">
                                    <input type="text" id="dealerFormSearch" class="search-input" placeholder="请输入经销商名称搜索..." autocomplete="off">
                                    <div id="dealerFormDropdown" class="search-dropdown-menu">
                                        <div class="search-dropdown-empty">请选择经销商</div>
                                        <!-- 经销商选项将通过JS动态加载 -->
                                    </div>
                                    <input type="hidden" id="dealerId" name="dealerId">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="px-6 py-4 flex justify-end space-x-3 border-t border-slate-200 bg-slate-50 rounded-b-2xl">
                <button type="button" class="px-4 py-2 bg-slate-200 text-slate-700 rounded-lg hover:bg-slate-300 transition-colors" onclick="$('#userFormModalContainer').addClass('hidden')">取消</button>
                <button type="button" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors" onclick="saveUser()">保存</button>
            </div>
        </div>
    </div>

    <!-- 重置密码模态框 -->
    <div id="resetPasswordModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
            <div class="p-5 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-xl font-semibold text-gray-800">重置密码</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#resetPasswordModalContainer').addClass('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 bg-gray-50">
                <form id="resetPasswordForm" class="space-y-5 bg-white p-5 rounded-md shadow-sm">
                    <input type="hidden" id="resetUserId" name="resetUserId">
                    
                    <div class="grid grid-cols-1 gap-5">
                        <div class="form-group">
                            <label for="newPassword" class="block text-sm font-medium text-gray-700 mb-1">新密码 <span class="text-red-500">*</span></label>
                            <input type="password" id="newPassword" name="newPassword" class="block w-full h-10 px-4 py-2 rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 text-base">
                            <p class="text-xs text-gray-500 mt-1">请输入新密码</p>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-1">确认密码 <span class="text-red-500">*</span></label>
                            <input type="password" id="confirmPassword" name="confirmPassword" class="block w-full h-10 px-4 py-2 rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 text-base">
                        </div>
                    </div>
                </form>
            </div>
            <div class="px-5 py-4 bg-gray-50 text-right rounded-b-lg flex justify-end space-x-3 border-t border-gray-200">
                <button type="button" class="px-5 py-2.5 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors text-base font-medium" onclick="$('#resetPasswordModalContainer').addClass('hidden')">取消</button>
                <button type="button" class="px-5 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors text-base font-medium" onclick="resetPassword()">提交</button>
            </div>
        </div>
    </div>

    <!-- 批量导入用户模态框 -->
    <div id="importUsersModalContainer" class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 hidden">
        <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col">
            <div class="p-5 border-b border-gray-200 flex justify-between items-center">
                <h3 class="text-xl font-semibold text-gray-800">批量导入用户</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="$('#importUsersModalContainer').addClass('hidden')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="p-6 bg-gray-50 overflow-y-auto">
                <form id="importUsersForm" class="space-y-6">
                    <div class="bg-white p-5 rounded-md shadow-sm">
                        <div class="mb-6">
                            <h4 class="text-lg font-medium text-gray-800 mb-2">导入说明</h4>
                            <ul class="list-disc list-inside text-sm text-gray-600 space-y-1">
                                <li>请下载Excel模板并按照要求填写用户信息</li>
                                <li>必填字段：用户名、角色(admin/teacher/publisher/dealer)、密码、所在学校/单位名称</li>
                                <li>可选字段：姓名、手机号</li>
                                <li>系统将根据角色和学校/单位名称自动匹配对应ID</li>
                                <li>若匹配不到对应学校/单位，该条记录将被跳过</li>
                                <li>最多支持一次导入500条记录</li>
                            </ul>
                        </div>
                        
                        <div class="flex mb-6">
                            <a href="/api/admin/download_user_import_template" class="bg-blue-100 text-blue-700 px-4 py-2 rounded hover:bg-blue-200 focus:outline-none flex items-center">
                                <i class="fas fa-download mr-2"></i>下载导入模板
                            </a>
                        </div>
                        
                        <div class="mb-6">
                            <label for="importFile" class="block text-sm font-medium text-gray-700 mb-2">
                                选择Excel文件 <span class="text-red-500">*</span>
                            </label>
                            <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                <div class="space-y-1 text-center">
                                    <i class="fas fa-file-excel text-4xl text-gray-400 mb-3"></i>
                                    <div class="flex text-sm text-gray-600">
                                        <label for="importFile" class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none">
                                            <span>选择文件</span>
                                            <input id="importFile" name="importFile" type="file" accept=".xlsx,.xls" class="sr-only" onchange="updateFileInfo(this)">
                                        </label>
                                        <p class="pl-1">或拖拽文件到此处</p>
                                    </div>
                                    <p class="text-xs text-gray-500">
                                        支持 .xlsx 或 .xls 格式
                                    </p>
                                    <div id="fileInfo" class="hidden mt-2 text-sm text-gray-700 bg-gray-100 p-2 rounded">
                                        <p id="fileName">未选择文件</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div id="importProgress" class="hidden">
                            <div class="mb-1 flex justify-between">
                                <span class="text-sm font-medium text-gray-700">上传进度</span>
                                <span id="progressPercent" class="text-sm font-medium text-gray-700">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                <div id="progressBar" class="bg-blue-600 h-2.5 rounded-full" style="width: 0%"></div>
                            </div>
                        </div>
                        
                        <div id="importResults" class="hidden mt-4 p-4 bg-gray-100 rounded-md">
                            <h5 class="font-medium text-gray-800 mb-2">导入结果</h5>
                            <div id="importResultDetails" class="text-sm"></div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="px-5 py-4 bg-gray-50 text-right rounded-b-lg flex justify-end space-x-3 border-t border-gray-200">
                <button type="button" class="px-5 py-2.5 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition-colors text-base font-medium" onclick="$('#importUsersModalContainer').addClass('hidden')">取消</button>
                <button type="button" id="startImportBtn" class="px-5 py-2.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition-colors text-base font-medium">开始导入</button>
            </div>
        </div>
    </div>

    <!-- 引入jQuery和AJAX -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        // 全局变量
        let currentPage = 1;
        let pageSize = 10;
        let totalPages = 1;
        let currentRole = 'all';
        let currentUserId = null;
        let isEditing = false;

        // 排序相关变量
        let sortField = '';
        let sortOrder = ''; // 'asc', 'desc', ''
        let sortTimeout = null; // 防抖定时器
        
        // 全局变量存储所有学校和单位数据
        let allSchools = [];
        let allCompanies = [];
        let activeSchools = []; // 有用户账户的学校
        let activeCompanies = []; // 有用户账户的单位
        let activePublishers = []; // 有用户账户的出版社
        let activeDealers = []; // 有用户账户的经销商
        
        $(document).ready(function() {
            // 初始化页面
            currentPage = 1;
            pageSize = 10;
            totalPages = 1;
            currentRole = 'all';
            currentUserId = null;
            
            // 加载学校列表
            loadSchools();
            
            // 加载出版社和经销商列表
            loadCompanies();
            
            // 加载筛选下拉框选项
            loadFilterOptions();
            
            // 初始化搜索下拉组件
            initSearchDropdowns();
            
            // 初始化筛选控件显示状态
            updateFiltersByRole(currentRole);
            
            // 初始化排序功能
            initSortHeaders();

            // 加载用户列表
            setTimeout(function() {
                loadUsers();
            }, 100); // 稍微延迟加载，确保筛选选项已经加载完成
            
            // 绑定角色标签点击事件
            $('#allTab, #adminTab, #teacherTab, #publisherTab, #dealerTab').click(function() {
                // 移除所有标签的活动状态
                $('#allTab, #adminTab, #teacherTab, #publisherTab, #dealerTab').removeClass('tab-active').addClass('tab-inactive');

                // 添加当前标签的活动状态
                $(this).removeClass('tab-inactive').addClass('tab-active');

                // 获取标签ID并加载相应数据
                const tabId = $(this).attr('id').replace('Tab', '');
                currentRole = tabId === 'all' ? 'all' : tabId;

                // 重置当前页
                currentPage = 1;

                // 清空搜索框
                $('#searchInput').val('');

                // 加载用户数据
                loadUsers();
            });
            
            // 绑定搜索按钮事件
            $('#searchBtn').click(function() {
                currentPage = 1;
                loadUsers();
            });
            
            // 绑定回车搜索
            $('#searchInput').keypress(function(e) {
                if (e.which === 13) {
                    currentPage = 1;
                    loadUsers();
                    return false;
                }
            });
            
            // 绑定学校和单位筛选变化事件
            $('#schoolFilter, #companyFilter').change(function() {
                currentPage = 1;
                loadUsers();
            });
            
            // 绑定分页按钮点击事件
            $('#firstPageBtn').click(function() {
                if (currentPage !== 1) {
                    currentPage = 1;
                    loadUsers();
                }
            });

            $('#prevPageBtn').click(function() {
                if (currentPage > 1) {
                    currentPage--;
                    loadUsers();
                }
            });

            $('#nextPageBtn').click(function() {
                if (currentPage < totalPages) {
                    currentPage++;
                    loadUsers();
                }
            });

            $('#lastPageBtn').click(function() {
                if (currentPage !== totalPages && totalPages > 0) {
                    currentPage = totalPages;
                    loadUsers();
                }
            });
            
            // 绑定添加用户按钮事件
            $('#addUserBtn').click(function() {
                showUserForm(false);
            });

            // 绑定刷新按钮事件
            $('#refreshBtn').click(function() {
                loadUsers();
            });
            
            // 绑定角色选择事件
            $('#role').change(function() {
                updateRoleFields();
            });
            
            // 绑定保存用户按钮事件
            $('#saveUserBtn').click(function() {
                saveUser();
            });
            
            // 绑定保存密码按钮事件
            $('#savePasswordBtn').click(function() {
                resetPassword();
            });
            
            // 绑定批量导入用户按钮事件
            $('#importUsersBtn').click(function() {
                // 重置导入表单状态
                document.getElementById('importUsersForm').reset();
                $('#fileInfo').addClass('hidden');
                showImportProgress(false);
                showImportResults(false);
                
                // 显示导入模态框
                $('#importUsersModalContainer').removeClass('hidden');
            });
            
            // 绑定开始导入按钮事件
            $('#startImportBtn').click(function() {
                importUsers();
            });
            
            // 直接绑定筛选框的事件
            $('#schoolFilterInput').on('input', function() {
                const query = $(this).val().toLowerCase();
                filterSchoolDropdown(query);
                // 确保下拉菜单显示
                $('#schoolDropdown').addClass('show');
            });
            
            $('#publisherFilterInput').on('input', function() {
                const query = $(this).val().toLowerCase();
                filterPublisherDropdown(query);
                // 确保下拉菜单显示
                $('#publisherDropdown').addClass('show');
            });
            
            $('#dealerFilterInput').on('input', function() {
                const query = $(this).val().toLowerCase();
                filterDealerDropdown(query);
                // 确保下拉菜单显示
                $('#dealerDropdown').addClass('show');
            });
            
            // 点击筛选框显示下拉菜单
            $('#schoolFilterInput').on('click', function(e) {
                e.stopPropagation();
                $('#schoolDropdown').addClass('show');
                $('#publisherDropdown, #dealerDropdown').removeClass('show');
            });
            
            $('#publisherFilterInput').on('click', function(e) {
                e.stopPropagation();
                $('#publisherDropdown').addClass('show');
                $('#schoolDropdown, #dealerDropdown').removeClass('show');
            });
            
            $('#dealerFilterInput').on('click', function(e) {
                e.stopPropagation();
                $('#dealerDropdown').addClass('show');
                $('#schoolDropdown, #publisherDropdown').removeClass('show');
            });
            
            // 点击文档其他地方关闭所有下拉菜单
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.search-dropdown').length) {
                    $('.search-dropdown-menu').removeClass('show');
                }
            });
        });

        // 加载学校列表
        function loadSchools() {
            $.ajax({
                url: '/api/admin/get_schools',
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        // 清空并填充学校选择框
                        $('#teacherSchool').empty().append('<option value="">-- 请选择 --</option>');
                        
                        response.data.forEach(function(school) {
                            $('#teacherSchool').append(`<option value="${school.id}">${school.name}</option>`);
                        });
                    } else {
                        showErrorModal(response.message || '加载学校列表失败');
                    }
                },
                error: function() {
                    showErrorModal('网络错误，请稍后重试');
                }
            });
        }
        
        // 加载公司列表（出版社和经销商）
        function loadCompanies() {
            // 清空公司列表
            allCompanies = [];
            
            // 加载学校列表
            $.ajax({
                url: '/api/admin/get_schools',
                type: 'GET',
                success: function(resp) {
                    if (resp.code === 0) {
                        allSchools = resp.data || [];
                        console.log(`获取到 ${allSchools.length} 所学校`);
                        
                        // 初始化学校下拉框会在initFormSearchDropdowns中进行
                    } else {
                        console.error('加载学校列表失败:', resp.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载学校列表请求失败:', error);
                }
            });
            
            // 加载出版社列表
            $.ajax({
                url: '/api/admin/get_publishers',
                type: 'GET',
                success: function(resp) {
                    if (resp.code === 0) {
                        const publishers = resp.data || [];
                        console.log(`获取到 ${publishers.length} 家出版社`);
                        
                        // 将出版社添加到公司列表
                        publishers.forEach(publisher => {
                            publisher.type = 'publisher';
                            allCompanies.push(publisher);
                        });
                        
                        // 初始化出版社下拉框会在initFormSearchDropdowns中进行
                    } else {
                        console.error('加载出版社列表失败:', resp.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载出版社列表请求失败:', error);
                }
            });
            
            // 加载经销商列表
            $.ajax({
                url: '/api/admin/get_dealers',
                type: 'GET',
                success: function(resp) {
                    if (resp.code === 0) {
                        const dealers = resp.data || [];
                        console.log(`获取到 ${dealers.length} 家经销商`);
                        
                        // 将经销商添加到公司列表
                        dealers.forEach(dealer => {
                            dealer.type = 'dealer';
                            allCompanies.push(dealer);
                        });
                        
                        // 初始化经销商下拉框会在initFormSearchDropdowns中进行
                    } else {
                        console.error('加载经销商列表失败:', resp.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载经销商列表请求失败:', error);
                }
            });
        }
        
        // 初始化排序表头
        function initSortHeaders() {
            $('.sort-header').click(function() {
                const field = $(this).data('field');
                handleSort(field);
            });
        }

        // 处理排序
        function handleSort(field) {
            // 清除之前的防抖定时器
            if (sortTimeout) {
                clearTimeout(sortTimeout);
            }

            if (sortField === field) {
                // 同一字段：升序 -> 降序 -> 取消排序
                if (sortOrder === 'asc') {
                    sortOrder = 'desc';
                } else if (sortOrder === 'desc') {
                    sortOrder = '';
                    sortField = '';
                } else {
                    sortOrder = 'asc';
                }
            } else {
                // 不同字段：直接设置为升序
                sortField = field;
                sortOrder = 'asc';
            }

            // 立即更新排序图标
            updateSortIcons();

            // 显示加载状态
            showSortLoading();

            // 防抖处理，200ms后执行排序
            sortTimeout = setTimeout(() => {
                // 重置到第一页并重新加载数据
                currentPage = 1;
                loadUsers();
            }, 200);
        }

        // 显示排序加载状态
        function showSortLoading() {
            $('#usersContainer').html(`
                <div class="px-6 py-12 text-center">
                    <div class="inline-flex items-center space-x-2 text-blue-600">
                        <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <span class="text-sm font-medium">正在排序...</span>
                    </div>
                </div>
            `);
        }

        // 更新排序图标
        function updateSortIcons() {
            // 重置所有图标
            $('.sort-icon').removeClass('text-blue-600').addClass('opacity-30');

            if (sortField && sortOrder) {
                // 找到当前排序字段的表头
                const currentHeader = $(`.sort-header[data-field="${sortField}"]`);

                if (sortOrder === 'asc') {
                    currentHeader.find('.sort-asc').removeClass('opacity-30').addClass('text-blue-600');
                } else if (sortOrder === 'desc') {
                    currentHeader.find('.sort-desc').removeClass('opacity-30').addClass('text-blue-600');
                }
            }
        }

        // 修改加载用户函数，适配新的筛选组件
        function loadUsers() {
            // 如果不是排序触发的加载，显示普通加载状态
            if (!$('#usersContainer').html().includes('正在排序')) {
                $('#usersContainer').html(`
                    <div class="px-6 py-12 text-center">
                        <div class="inline-flex items-center space-x-2 text-blue-600">
                            <svg class="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <span class="text-sm font-medium">加载中...</span>
                        </div>
                    </div>
                `);
            }
            // 获取搜索关键词
            const keyword = $('#searchInput').val();
            const schoolId = $('#schoolFilter').val();
            const publisherId = $('#publisherFilter').val();
            const dealerId = $('#dealerFilter').val();
            
            // 显示加载状态
            $('#usersContainer').html('<div class="flex justify-center items-center p-10"><div class="spinner-border text-blue-500" role="status"><span class="sr-only">加载中...</span></div></div>');
            
            // 构建请求参数
            const requestData = {
                page: currentPage,
                page_size: pageSize,
                keyword: keyword
            };

            // 只有当不是"全部"时才添加角色筛选
            if (currentRole !== 'all' && currentRole !== '') {
                requestData.role = currentRole;
            }

            // 只有当不是"all"时才添加筛选条件
            if (schoolId && schoolId !== 'all') {
                requestData.school_id = schoolId;
            }

            if (publisherId && publisherId !== 'all') {
                requestData.publisher_id = publisherId;
            }

            if (dealerId && dealerId !== 'all') {
                requestData.dealer_id = dealerId;
            }

            // 添加排序参数
            if (sortField && sortOrder) {
                requestData.sort_field = sortField;
                requestData.sort_order = sortOrder;
            }
            
            $.ajax({
                url: '/api/admin/get_users',
                type: 'GET',
                data: requestData,
                success: function(response) {
                    if (response.code === 0) {
                        if (response.data.users && response.data.users.length > 0) {
                            renderUsers(response.data.users);
                            renderPagination(response.data.total_count);
                        } else {
                            $('#usersContainer').html('<div class="text-center p-6 text-gray-500">没有找到匹配的用户</div>');
                        }
                        updateRoleCounts(response.data.role_counts);
                    } else {
                        showErrorModal(response.message || '加载用户列表失败');
                        $('#usersContainer').html('<div class="text-center p-6 text-gray-500">加载失败，请稍后重试</div>');
                    }
                },
                error: function() {
                    showErrorModal('网络错误，请稍后重试');
                    $('#usersContainer').html('<div class="text-center p-6 text-gray-500">网络错误，请稍后重试</div>');
                }
            });
        }
        
        // 渲染用户列表
        function renderUsers(users) {
            if (!users || users.length === 0) {
                $('#usersContainer').html('<div class="text-center p-6 text-gray-500">没有找到匹配的用户</div>');
                return;
            }
            
            console.log('渲染用户列表，第一个用户:', users[0]); // 查看用户对象结构
            
            let html = '';
            
            users.forEach(function(user) {
                // 确保使用正确的用户ID属性
                const userId = user.id || user.user_id; // 兼容可能的属性名
                console.log(`用户${user.username}的ID:`, userId);
                
                // 根据角色获取图标、名称和样式（与tab栏保持一致）
                let roleIcon = 'fas fa-user';
                let roleName = '未知';
                let roleBadgeClass = 'bg-slate-100 text-slate-800';

                if (user.role === 'admin') {
                    roleIcon = 'fas fa-user-shield';
                    roleName = '管理员';
                    roleBadgeClass = 'bg-red-100 text-red-800';
                } else if (user.role === 'teacher') {
                    roleIcon = 'fas fa-chalkboard-teacher';
                    roleName = '教师';
                    roleBadgeClass = 'bg-green-100 text-green-800';
                } else if (user.role === 'publisher') {
                    roleIcon = 'fas fa-building';
                    roleName = '出版社';
                    roleBadgeClass = 'bg-blue-100 text-blue-800';
                } else if (user.role === 'dealer') {
                    roleIcon = 'fas fa-store';
                    roleName = '经销商';
                    roleBadgeClass = 'bg-purple-100 text-purple-800';
                }
                
                // 根据角色确定显示的单位信息
                let organizationInfo = '';
                if (user.role === 'teacher' && user.school_name) {
                    organizationInfo = `
                    <div class="flex items-center space-x-2 text-sm text-slate-600">
                        <i class="fas fa-graduation-cap text-slate-400 w-4"></i>
                        <span>${user.school_name}</span>
                    </div>`;
                } else if (user.role === 'publisher' && user.publisher_company_name) {
                    organizationInfo = `
                    <div class="flex items-center space-x-2 text-sm text-slate-600">
                        <i class="fas fa-building text-slate-400 w-4"></i>
                        <span>${user.publisher_company_name}</span>
                    </div>`;
                } else if (user.role === 'dealer' && user.dealer_company_name) {
                    organizationInfo = `
                    <div class="flex items-center space-x-2 text-sm text-slate-600">
                        <i class="fas fa-store text-slate-400 w-4"></i>
                        <span>${user.dealer_company_name}</span>
                    </div>`;
                }

                // 格式化注册时间
                const createdAt = user.created_at ? new Date(user.created_at).toLocaleDateString('zh-CN') : '未知';

                // 获取机构信息
                let organizationText = '未设置';
                if (user.role === 'teacher' && user.school_name) {
                    organizationText = user.school_name;
                } else if (user.role === 'publisher' && user.publisher_company_name) {
                    organizationText = user.publisher_company_name;
                } else if (user.role === 'dealer' && user.dealer_company_name) {
                    organizationText = user.dealer_company_name;
                }

                // 构建用户列表行
                html += `
                <div class="px-6 py-4 border-b border-slate-100 hover:bg-slate-50 transition-colors">
                    <div class="grid grid-cols-12 gap-4 items-center">
                        <!-- 用户信息 -->
                        <div class="col-span-2">
                            <div class="min-w-0">
                                <div class="font-medium text-slate-800 truncate" title="${user.name || '未设置姓名'}">${user.name || '未设置姓名'}</div>
                                <div class="text-sm text-slate-500 truncate" title="@${user.username}">@${user.username}</div>
                            </div>
                        </div>

                        <!-- 角色 -->
                        <div class="col-span-1">
                            <span class="px-2 py-1 ${roleBadgeClass} text-xs font-medium rounded-full flex items-center space-x-1 w-fit">
                                <i class="${roleIcon} text-xs"></i>
                                <span>${roleName}</span>
                            </span>
                        </div>

                        <!-- 联系方式 -->
                        <div class="col-span-2 min-w-0">
                            <div class="space-y-1">
                                <div class="flex items-center space-x-1 text-sm text-slate-600 min-w-0">
                                    <i class="fas fa-phone text-slate-400 text-xs flex-shrink-0"></i>
                                    <span class="truncate" title="${user.phone || '未设置'}">${user.phone || '未设置'}</span>
                                </div>
                                ${user.email ? `
                                <div class="flex items-center space-x-1 text-sm text-slate-600 min-w-0">
                                    <i class="fas fa-envelope text-slate-400 text-xs flex-shrink-0"></i>
                                    <span class="truncate" title="${user.email}">${user.email}</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>

                        <!-- 所属机构 -->
                        <div class="col-span-3 min-w-0">
                            <div class="flex items-center space-x-2 text-sm text-slate-600 min-w-0">
                                <i class="fas fa-${user.role === 'teacher' ? 'graduation-cap' : user.role === 'publisher' ? 'building' : user.role === 'dealer' ? 'store' : 'user-shield'} text-slate-400 text-xs flex-shrink-0"></i>
                                <span class="truncate" title="${organizationText}">${organizationText}</span>
                            </div>
                        </div>

                        <!-- 注册时间 -->
                        <div class="col-span-2">
                            <span class="text-sm text-slate-600">${createdAt}</span>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="col-span-2">
                            <div class="flex items-center justify-end space-x-1">
                                <button type="button" class="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" onclick="viewUserDetail(${userId})" title="查看详情">
                                    <i class="fas fa-eye text-sm"></i>
                                </button>
                                <button type="button" class="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors" onclick="editUser(${userId})" title="编辑用户">
                                    <i class="fas fa-edit text-sm"></i>
                                </button>
                                <button type="button" class="p-2 text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors" onclick="showResetPasswordModal(${userId})" title="重置密码">
                                    <i class="fas fa-key text-sm"></i>
                                </button>
                                <button type="button" class="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors" onclick="showDeleteConfirmation(${userId})" title="删除用户">
                                    <i class="fas fa-trash-alt text-sm"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                `;
            });

            // 如果没有用户数据，显示空状态
            if (users.length === 0) {
                html = `
                <div class="px-6 py-12 text-center">
                    <div class="w-16 h-16 mx-auto mb-4 bg-slate-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-users text-slate-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-slate-800 mb-2">暂无用户数据</h3>
                    <p class="text-slate-500 mb-4">当前筛选条件下没有找到用户</p>
                    <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors" onclick="$('#searchInput').val(''); loadUsers();">
                        清除筛选
                    </button>
                </div>
                `;
            }

            $('#usersContainer').html(html);
        }
        
        // 页码生成函数
        function getPageNumbers(currentPage, totalPages) {
            const pageNumbers = [];

            if (totalPages <= 7) {
                // 总页数不超过7页，显示所有页码
                for (let i = 1; i <= totalPages; i++) {
                    pageNumbers.push(i);
                }
            } else {
                // 总页数超过7页，使用省略号
                pageNumbers.push(1);

                if (currentPage <= 4) {
                    // 当前页在前部
                    pageNumbers.push(2, 3, 4, 5);
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages);
                } else if (currentPage >= totalPages - 3) {
                    // 当前页在后部
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages - 4, totalPages - 3, totalPages - 2, totalPages - 1);
                    pageNumbers.push(totalPages);
                } else {
                    // 当前页在中部
                    pageNumbers.push('...');
                    pageNumbers.push(currentPage - 1, currentPage, currentPage + 1);
                    pageNumbers.push('...');
                    pageNumbers.push(totalPages);
                }
            }

            return pageNumbers;
        }

        // 渲染页码按钮
        function renderPageNumbers(containerSelector, currentPage, totalPages, clickHandler) {
            const container = $(containerSelector);
            container.empty();

            const pageNumbers = getPageNumbers(currentPage, totalPages);

            pageNumbers.forEach(pageNumber => {
                if (pageNumber === '...') {
                    // 省略号
                    container.append(`
                        <span class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md bg-white text-gray-700">
                            ...
                        </span>
                    `);
                } else {
                    // 页码按钮
                    const isActive = pageNumber === currentPage;
                    const activeClass = isActive ? 'bg-blue-50 text-blue-600 border-blue-500' : 'bg-white text-gray-700';

                    container.append(`
                        <button data-page="${pageNumber}"
                                class="relative inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md hover:bg-gray-50 ${activeClass}">
                            ${pageNumber}
                        </button>
                    `);
                }
            });

            // 绑定页码点击事件
            container.off('click', 'button[data-page]').on('click', 'button[data-page]', function() {
                const page = parseInt($(this).data('page'));
                if (page && page !== currentPage) {
                    clickHandler(page);
                }
            });
        }

        // 渲染分页
        function renderPagination(totalCount) {
            totalPages = Math.ceil(totalCount / pageSize);

            // 更新显示信息
            $('#currentPageDisplay').text(currentPage);
            $('#totalPagesDisplay').text(totalPages);
            $('#totalCountDisplay').text(totalCount);

            // 更新按钮状态
            $('#firstPageBtn').prop('disabled', currentPage <= 1);
            $('#prevPageBtn').prop('disabled', currentPage <= 1);
            $('#nextPageBtn').prop('disabled', currentPage >= totalPages);
            $('#lastPageBtn').prop('disabled', currentPage >= totalPages);

            // 渲染页码
            renderPageNumbers('#pageNumbers', currentPage, totalPages, function(page) {
                currentPage = page;
                loadUsers();
            });
        }
        
        // 更新角色统计
        function updateRoleCounts(roleCounts) {
            // 更新每个角色标签上的计数
            $('#allCount').text(roleCounts.all || 0);
            $('#adminCount').text(roleCounts.admin || 0);
            $('#teacherCount').text(roleCounts.teacher || 0);
            $('#publisherCount').text(roleCounts.publisher || 0);
            $('#dealerCount').text(roleCounts.dealer || 0);
        }
        
        // 显示用户表单（新增或编辑）
        function showUserForm(isEdit, userData = null) {
            console.log('显示用户表单:', isEdit ? '编辑' : '添加', userData);
            
            // 重置表单
            const userForm = document.getElementById('userForm');
            if (userForm) {
                userForm.reset();
            } else {
                console.error('表单元素不存在:', '#userForm');
                return; // 如果表单不存在，则不继续执行
            }
            $('#userId').val('');
            
            isEditing = isEdit;
            
            // 设置模态框标题
            $('#userFormTitle').text(isEdit ? '编辑用户' : '添加用户');
            
            // 显示或隐藏密码字段
            $('#passwordField').toggleClass('hidden', isEdit);
            
            // 设置用户名字段是否可编辑
            $('#username').prop('disabled', isEdit);
            
            if (isEdit && userData) {
                console.log('填充用户数据:', userData);
                
                // 填充表单数据
                $('#userId').val(userData.user_id || userData.id);
                $('#username').val(userData.username);
                $('#role').val(userData.role);
                $('#name').val(userData.name);
                $('#phone').val(userData.phone_number || userData.phone);
                $('#email').val(userData.email || '');
                $('#contact_info').val(userData.contact_info || '');
                
                // 根据角色显示特定字段
                updateRoleFields();
                
                // 设置特定角色字段的值
                if (userData.role === 'teacher' && userData.teacher_school_id) {
                    $('#schoolId').val(userData.teacher_school_id);
                    // 查找学校名称
                    const school = allSchools.find(s => s.id == userData.teacher_school_id);
                    if (school) {
                        $('#schoolFormSearch').val(school.name);
                    }
                } else if (userData.role === 'publisher' && userData.publisher_company_id) {
                    $('#publisherId').val(userData.publisher_company_id);
                    // 查找出版社名称
                    const publisher = allCompanies.find(c => c.id == userData.publisher_company_id && c.type === 'publisher');
                    if (publisher) {
                        $('#publisherFormSearch').val(publisher.name);
                    }
                } else if (userData.role === 'dealer' && userData.dealer_company_id) {
                    $('#dealerId').val(userData.dealer_company_id);
                    // 查找经销商名称
                    const dealer = allCompanies.find(c => c.id == userData.dealer_company_id && c.type === 'dealer');
                    if (dealer) {
                        $('#dealerFormSearch').val(dealer.name);
                    }
                }
                
                if (userData.role === 'teacher') {
                    $('#departmentField').removeClass('hidden');
                    $('#positionField').removeClass('hidden');
                    $('#department').val(userData.department || '');
                    $('#position').val(userData.position || '');
                }
            } else {
                // 隐藏所有特定角色字段
                updateRoleFields();
            }
            
            // 加载学校和公司列表
            loadFormOptions();
            
            // 显示模态框
            $('#userFormModalContainer').removeClass('hidden');
            
            // 立即初始化表单中的搜索下拉框
            initFormSearchDropdowns();
            console.log('初始化表单搜索下拉框完成');
        }
        
        // 加载表单选项
        function loadFormOptions() {
            // 清空公司列表
            allCompanies = [];
            
            // 加载学校列表
            $.ajax({
                url: '/api/admin/get_schools',
                type: 'GET',
                success: function(resp) {
                    if (resp.code === 0) {
                        allSchools = resp.data || [];
                        console.log(`获取到 ${allSchools.length} 所学校`);
                        
                        // 初始化学校下拉框会在initFormSearchDropdowns中进行
                    } else {
                        console.error('加载学校列表失败:', resp.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载学校列表请求失败:', error);
                }
            });
            
            // 加载出版社列表
            $.ajax({
                url: '/api/admin/get_publishers',
                type: 'GET',
                success: function(resp) {
                    if (resp.code === 0) {
                        const publishers = resp.data || [];
                        console.log(`获取到 ${publishers.length} 家出版社`);
                        
                        // 将出版社添加到公司列表
                        publishers.forEach(publisher => {
                            publisher.type = 'publisher';
                            allCompanies.push(publisher);
                        });
                        
                        // 初始化出版社下拉框会在initFormSearchDropdowns中进行
                    } else {
                        console.error('加载出版社列表失败:', resp.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载出版社列表请求失败:', error);
                }
            });
            
            // 加载经销商列表
            $.ajax({
                url: '/api/admin/get_dealers',
                type: 'GET',
                success: function(resp) {
                    if (resp.code === 0) {
                        const dealers = resp.data || [];
                        console.log(`获取到 ${dealers.length} 家经销商`);
                        
                        // 将经销商添加到公司列表
                        dealers.forEach(dealer => {
                            dealer.type = 'dealer';
                            allCompanies.push(dealer);
                        });
                        
                        // 初始化经销商下拉框会在initFormSearchDropdowns中进行
                    } else {
                        console.error('加载经销商列表失败:', resp.msg);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('加载经销商列表请求失败:', error);
                }
            });
        }
        
        // 根据选择的角色更新表单字段
        function updateRoleFields() {
            const role = $('#role').val();
            
            // 隐藏所有特定角色字段
            $('#schoolField, #publisherField, #dealerField, #departmentField, #positionField').addClass('hidden');
            
            // 清空隐藏字段的值
            $('#schoolId, #publisherId, #dealerId, #department, #position').val('');
            $('#schoolFormSearch, #publisherFormSearch, #dealerFormSearch').val('');
            
            // 根据角色显示对应字段
            if (role === 'teacher') {
                $('#schoolField, #departmentField, #positionField').removeClass('hidden');
            } else if (role === 'publisher') {
                $('#publisherField').removeClass('hidden');
            } else if (role === 'dealer') {
                $('#dealerField').removeClass('hidden');
            }
        }
        
        // 查看用户详情
        function viewUserDetail(userId) {
            $.ajax({
                url: `/api/admin/get_user_detail?user_id=${userId}`,
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        // 显示用户详情模态框
                        showUserDetailModal(response.data);
                    } else {
                        showErrorModal(response.message || '获取用户详情失败');
                    }
                },
                error: function() {
                    showErrorModal('网络错误，请稍后重试');
                }
            });
        }
        
        // 编辑用户
        function editUser(id) {
            console.log('编辑用户ID:', id);
            $.ajax({
                url: `/api/admin/get_user_detail?user_id=${id}`,
                type: 'GET',
                success: function(response) {
                    console.log('获取用户详情响应:', response);
                    if (response.code === 0) {
                        // 显示编辑表单
                        showUserForm(true, response.data);
                    } else {
                        showErrorModal(response.message || '获取用户详情失败');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('获取用户详情错误:', error, xhr.responseText);
                    showErrorModal('网络错误，请稍后重试');
                }
            });
        }
        
        // 显示重置密码模态框
        function showResetPasswordModal(userId) {
            currentUserId = userId;
            $('#resetUserId').val(userId);
            $('#newPassword, #confirmPassword').val('');
            $('#resetPasswordModalContainer').removeClass('hidden');
        }
        
        // 重置密码
        function resetPassword() {
            const newPassword = $('#newPassword').val();
            const confirmPassword = $('#confirmPassword').val();
            
            // 验证密码
            if (!newPassword) {
                showErrorModal('请输入新密码');
                return;
            }
            
            if (newPassword !== confirmPassword) {
                showErrorModal('两次输入的密码不一致');
                return;
            }
            

            
            // 发送重置密码请求
            $.ajax({
                url: '/api/admin/reset_password',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    user_id: currentUserId,
                    new_password: newPassword
                }),
                success: function(response) {
                    if (response.code === 0) {
                        // 关闭模态框
                        $('#resetPasswordModalContainer').addClass('hidden');
                        // 显示成功提示
                        showSuccessModal('密码重置成功');
                    } else {
                        showErrorModal(response.message || '密码重置失败');
                    }
                },
                error: function() {
                    showErrorModal('网络错误，请稍后重试');
                }
            });
        }
        
        // 显示删除确认模态框
        function showDeleteConfirmation(userId) {
            currentUserId = userId;
            // 显示确认模态框
            $('#confirmModalTitle').text('删除用户');
            $('#confirmModalContent').text('确定要删除此用户吗？此操作不可逆，用户相关的所有数据将被永久删除。');
            $('#confirmModalYesBtn').attr('onclick', 'deleteUser()');
            $('#confirmModalContainer').removeClass('hidden');
        }
        
        // 删除用户
        function deleteUser() {
            $.ajax({
                url: '/api/admin/delete_user',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    user_id: currentUserId
                }),
                success: function(response) {
                    // 关闭确认模态框
                    $('#confirmModalContainer').addClass('hidden');
                    
                    if (response.code === 0) {
                        // 显示成功提示
                        showSuccessModal('用户删除成功');
                        // 重新加载用户列表
                        loadUsers();
                    } else {
                        showErrorModal(response.message || '删除用户失败');
                    }
                },
                error: function() {
                    // 关闭确认模态框
                    $('#confirmModalContainer').addClass('hidden');
                    showErrorModal('网络错误，请稍后重试');
                }
            });
        }
        
        // 保存用户
        function saveUser() {
            // 获取表单数据
            const userId = $('#userId').val();
            const username = $('#username').val();
            const role = $('#role').val();
            const name = $('#name').val();
            const phone = $('#phone').val();
            const email = $('#email').val();
            const contact_info = $('#contact_info').val();
            const password = $('#password').val();
            
            // 基本验证
            if (!username) {
                showErrorModal('请输入用户名');
                return;
            }
            
            if (!role) {
                showErrorModal('请选择用户角色');
                return;
            }
            
            if (!name) {
                showErrorModal('请输入姓名');
                return;
            }
            
            if (!phone) {
                showErrorModal('请输入手机号');
                return;
            }

            if (!email) {
                showErrorModal('请输入邮箱');
                return;
            }

            // 验证邮箱格式
            const emailPattern = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            if (!emailPattern.test(email)) {
                showErrorModal('请输入正确的邮箱地址');
                return;
            }

            // 新增用户时验证密码
            if (!isEditing && !password) {
                showErrorModal('请输入密码');
                return;
            }
            

            
            // 准备提交的数据
            const data = {
                username: username,
                role: role,
                name: name,
                phone_number: phone,
                email: email,
                contact_info: contact_info
            };
            
            // 添加特定角色的字段
            if (role === 'teacher') {
                data.school_id = $('#schoolId').val();
                if (!data.school_id) {
                    showErrorModal('请选择所属学校');
                    return;
                }
                data.department = $('#department').val();
                data.position = $('#position').val();
            } else if (role === 'publisher') {
                data.publisher_company_id = $('#publisherId').val();
                if (!data.publisher_company_id) {
                    showErrorModal('请选择所属出版社');
                    return;
                }
            } else if (role === 'dealer') {
                data.dealer_company_id = $('#dealerId').val();
                if (!data.dealer_company_id) {
                    showErrorModal('请选择所属经销商');
                    return;
                }
            }
            
            // 如果是新增用户，添加密码
            if (!isEditing) {
                data.password = password;
            }
            
            // 如果是编辑用户，添加用户ID
            if (isEditing) {
                data.user_id = userId;
            }
            
            // 发送请求
            $.ajax({
                url: isEditing ? '/api/admin/edit_user' : '/api/admin/add_user',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(data),
                success: function(response) {
                    if (response.code === 0) {
                        // 关闭模态框
                        $('#userFormModalContainer').addClass('hidden');
                        // 显示成功提示
                        showSuccessModal(isEditing ? '用户信息更新成功' : '用户添加成功');
                        // 重新加载用户列表
                        loadUsers();
                    } else {
                        showErrorModal(response.message || (isEditing ? '更新用户失败' : '添加用户失败'));
                    }
                },
                error: function() {
                    showErrorModal('网络错误，请稍后重试');
                }
            });
        }
        
        // 显示用户详情模态框
        function showUserDetailModal(user) {
            console.log('用户详情数据:', user); // 调试用

            // 获取角色名称和样式（与tab栏保持一致）
            let roleName = '未知';
            let roleClass = 'bg-slate-100 text-slate-800';
            let roleIcon = 'fas fa-user';

            if (user.role === 'admin') {
                roleName = '管理员';
                roleClass = 'bg-red-100 text-red-800';
                roleIcon = 'fas fa-user-shield';
            } else if (user.role === 'teacher') {
                roleName = '教师';
                roleClass = 'bg-green-100 text-green-800';
                roleIcon = 'fas fa-chalkboard-teacher';
            } else if (user.role === 'publisher') {
                roleName = '出版社';
                roleClass = 'bg-blue-100 text-blue-800';
                roleIcon = 'fas fa-building';
            } else if (user.role === 'dealer') {
                roleName = '经销商';
                roleClass = 'bg-purple-100 text-purple-800';
                roleIcon = 'fas fa-store';
            }

            // 构建详情内容HTML - 按照设计规范优化
            let contentHtml = `
                <div class="space-y-8">
                    <!-- 用户头像和基本信息 -->
                    <div class="flex items-start space-x-6">
                        <div class="w-20 h-20 bg-gradient-to-br from-slate-100 to-slate-200 rounded-2xl flex items-center justify-center text-2xl font-bold text-slate-600 shadow-sm">
                            ${user.name ? user.name.charAt(0).toUpperCase() : 'U'}
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-3 mb-2">
                                <h2 class="text-2xl font-semibold text-slate-800">${user.name || '未设置姓名'}</h2>
                                <span class="px-3 py-1 rounded-full text-sm font-medium ${roleClass} flex items-center space-x-1">
                                    <i class="${roleIcon} text-xs"></i>
                                    <span>${roleName}</span>
                                </span>
                            </div>
                            <p class="text-slate-500 text-lg">@${user.username}</p>
                            <p class="text-slate-400 text-sm mt-1">用户ID: ${user.user_id || user.id}</p>
                        </div>
                    </div>

                    <!-- 基本信息卡片 -->
                    <div class="bg-slate-50 rounded-2xl p-6">
                        <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center space-x-2">
                            <i class="fas fa-info-circle text-blue-500"></i>
                            <span>基本信息</span>
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">用户名</p>
                                <p class="text-slate-800 font-medium">${user.username}</p>
                            </div>
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">角色</p>
                                <p class="text-slate-800 font-medium flex items-center space-x-2">
                                    <i class="${roleIcon} text-slate-600 text-sm"></i>
                                    <span>${roleName}</span>
                                </p>
                            </div>
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">姓名</p>
                                <p class="text-slate-800 font-medium">${user.name || '未设置'}</p>
                            </div>
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">手机号</p>
                                <p class="text-slate-800 font-medium flex items-center space-x-2">
                                    <i class="fas fa-phone text-slate-400 text-xs"></i>
                                    <span>${user.phone_number || user.phone || '未设置'}</span>
                                </p>
                            </div>
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">邮箱</p>
                                <p class="text-slate-800 font-medium flex items-center space-x-2">
                                    <i class="fas fa-envelope text-slate-400 text-xs"></i>
                                    <span>${user.email || '未设置'}</span>
                                </p>
                            </div>
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">创建时间</p>
                                <p class="text-slate-800 font-medium flex items-center space-x-2">
                                    <i class="fas fa-calendar text-slate-400 text-xs"></i>
                                    <span>${user.created_at || user.create_time || '未知'}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    ${user.role === 'teacher' && user.school ? `
                    <!-- 学校信息卡片 -->
                    <div class="bg-blue-50 rounded-2xl p-6">
                        <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center space-x-2">
                            <i class="fas fa-graduation-cap text-blue-500"></i>
                            <span>学校信息</span>
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">所属学校</p>
                                <p class="text-slate-800 font-medium">${user.school.name || '未设置'}</p>
                            </div>
                            ${user.school.main_address ? `
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">学校地址</p>
                                <p class="text-slate-800 font-medium">${user.school.main_address.province || ''}${user.school.main_address.city || ''}${user.school.main_address.district || ''}${user.school.main_address.detailed_address || ''}</p>
                            </div>
                            ` : ''}
                            ${user.school.main_address && user.school.main_address.campus_name ? `
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">校区名称</p>
                                <p class="text-slate-800 font-medium">${user.school.main_address.campus_name}</p>
                            </div>
                            ` : ''}
                            ${user.school.main_address && user.school.main_address.contact_phone ? `
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">学校电话</p>
                                <p class="text-slate-800 font-medium">${user.school.main_address.contact_phone}</p>
                            </div>
                            ` : ''}
                        </div>
                        ${user.school.addresses && user.school.addresses.length > 1 ? `
                        <div class="mt-4 pt-4 border-t border-blue-200">
                            <p class="text-sm font-medium text-slate-500 mb-2">其他校区</p>
                            <div class="space-y-2">
                                ${user.school.addresses.slice(1).map(addr => `
                                    <div class="bg-blue-100 rounded-lg p-3">
                                        <p class="font-medium text-blue-800">${addr.campus_name || '校区'}</p>
                                        <p class="text-sm text-blue-600">${addr.province || ''}${addr.city || ''}${addr.district || ''}${addr.detailed_address || ''}</p>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}
                        ${user.courses && user.courses.length > 0 ? `
                        <div class="mt-4 pt-4 border-t border-blue-200">
                            <p class="text-sm font-medium text-slate-500 mb-2">教授课程</p>
                            <div class="flex flex-wrap gap-2">
                                ${user.courses.map(course => `
                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-lg text-sm font-medium">
                                        ${course.course_name} (${course.semester})
                                    </span>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}
                    </div>
                    ` : ''}

                    ${(user.role === 'publisher' || user.role === 'dealer') && user.company ? `
                    <!-- 单位信息卡片 -->
                    <div class="bg-${user.role === 'publisher' ? 'green' : 'purple'}-50 rounded-2xl p-6">
                        <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center space-x-2">
                            <i class="fas fa-${user.role === 'publisher' ? 'building' : 'store'} text-${user.role === 'publisher' ? 'green' : 'purple'}-500"></i>
                            <span>${user.role === 'publisher' ? '出版社' : '经销商'}信息</span>
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">所属${user.role === 'publisher' ? '出版社' : '经销商'}</p>
                                <p class="text-slate-800 font-medium">${user.company.name || '未设置'}</p>
                            </div>
                            ${user.company.main_address ? `
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">${user.role === 'publisher' ? '出版社' : '经销商'}地址</p>
                                <p class="text-slate-800 font-medium">${user.company.main_address.province || ''}${user.company.main_address.city || ''}${user.company.main_address.district || ''}${user.company.main_address.detailed_address || ''}</p>
                            </div>
                            ` : ''}
                            ${user.company.main_address && user.company.main_address.office_name ? `
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">办公地点</p>
                                <p class="text-slate-800 font-medium">${user.company.main_address.office_name}</p>
                            </div>
                            ` : ''}
                            ${user.company.main_address && user.company.main_address.contact_phone ? `
                            <div class="space-y-1">
                                <p class="text-sm font-medium text-slate-500">联系电话</p>
                                <p class="text-slate-800 font-medium">${user.company.main_address.contact_phone}</p>
                            </div>
                            ` : ''}
                        </div>
                        ${user.company.addresses && user.company.addresses.length > 1 ? `
                        <div class="mt-4 pt-4 border-t border-${user.role === 'publisher' ? 'green' : 'purple'}-200">
                            <p class="text-sm font-medium text-slate-500 mb-2">其他办公地点</p>
                            <div class="space-y-2">
                                ${user.company.addresses.slice(1).map(addr => `
                                    <div class="bg-${user.role === 'publisher' ? 'green' : 'purple'}-100 rounded-lg p-3">
                                        <p class="font-medium text-${user.role === 'publisher' ? 'green' : 'purple'}-800">${addr.office_name || '办公地点'}</p>
                                        <p class="text-sm text-${user.role === 'publisher' ? 'green' : 'purple'}-600">${addr.province || ''}${addr.city || ''}${addr.district || ''}${addr.detailed_address || ''}</p>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        ` : ''}
                        ${user.permissions ? `
                        <div class="mt-4 pt-4 border-t border-${user.role === 'publisher' ? 'green' : 'purple'}-200">
                            <p class="text-sm font-medium text-slate-500 mb-2">权限设置</p>
                            <div class="grid grid-cols-2 gap-3">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-${user.permissions.can_recommend_books ? 'check-circle text-green-500' : 'times-circle text-red-500'} text-sm permission-icon"></i>
                                    <span class="text-sm text-slate-700">推荐图书</span>
                                </div>
                                ${user.role === 'dealer' ? `
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-${user.permissions.can_invite_users ? 'check-circle text-green-500' : 'times-circle text-red-500'} text-sm permission-icon"></i>
                                    <span class="text-sm text-slate-700">邀请用户</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-${user.permissions.can_initiate_exhibition ? 'check-circle text-green-500' : 'times-circle text-red-500'} text-sm permission-icon"></i>
                                    <span class="text-sm text-slate-700">发起展会</span>
                                </div>
                                ` : ''}
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-${user.permissions.can_register_exhibition ? 'check-circle text-green-500' : 'times-circle text-red-500'} text-sm permission-icon"></i>
                                    <span class="text-sm text-slate-700">报名展会</span>
                                </div>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                    ` : ''}
                </div>
            `;

            // 填充到现有的模态框中
            $('#userDetailContent').html(contentHtml);
            $('#userDetailModalContainer').removeClass('hidden');
        }
        
        // 关闭用户详情模态框
        function closeUserDetailModal() {
            $('#userDetailModalContainer').addClass('hidden');
        }
        
        // 显示错误模态框
        function showErrorModal(message) {
            $('#errorModalContent').text(message);
            $('#errorModalContainer').removeClass('hidden');
        }
        
        // 显示成功模态框
        function showSuccessModal(message) {
            $('#successModalContent').text(message);
            $('#successModalContainer').removeClass('hidden');
            
            // 3秒后自动关闭
            setTimeout(function() {
                $('#successModalContainer').addClass('hidden');
            }, 3000);
        }
        


        // 新增加载筛选选项函数
        function loadFilterOptions() {
            // 加载学校列表
            $.ajax({
                url: '/api/admin/get_users_schools',
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        activeSchools = response.data || [];
                        console.log(`获取到 ${activeSchools.length} 所活跃学校`);
                    } else {
                        console.error('获取活跃学校列表失败:', response.message);
                        activeSchools = [];
                    }
                },
                error: function(xhr, status, error) {
                    console.error('获取活跃学校列表请求失败:', error);
                    activeSchools = [];
                }
            });
            
            // 加载公司列表（出版社和经销商）
            $.ajax({
                url: '/api/admin/get_users_companies',
                type: 'GET',
                success: function(response) {
                    if (response.code === 0) {
                        const companies = response.data || [];
                        console.log(`获取到 ${companies.length} 家活跃公司`);
                        
                        // 分离出版社和经销商
                        activePublishers = companies.filter(company => company.type === 'publisher');
                        activeDealers = companies.filter(company => company.type === 'dealer');
                        
                        console.log(`分离出 ${activePublishers.length} 家出版社和 ${activeDealers.length} 家经销商`);
                    } else {
                        console.error('获取活跃公司列表失败:', response.message);
                        activePublishers = [];
                        activeDealers = [];
                    }
                },
                error: function(xhr, status, error) {
                    console.error('获取活跃公司列表请求失败:', error);
                    activePublishers = [];
                    activeDealers = [];
                }
            });
        }

        // 点击外部区域关闭下拉框
        $(document).click(function(event) {
            if (!$(event.target).closest('.custom-select-container').length) {
                $('.dropdown-list').addClass('hidden');
            }
        });

        // 学校下拉框相关函数
        function toggleSchoolDropdown() {
            $('#schoolDropdown').toggleClass('hidden');
            $('#companyDropdown').addClass('hidden'); // 关闭另一个下拉框
            
            // 当下拉框显示时，聚焦输入框
            if (!$('#schoolDropdown').hasClass('hidden')) {
                $('#schoolFilterInput').focus();
            }
        }
        
        function selectSchool(id) {
            $('#schoolFilter').val(id);
            
            let selectedText = '全部学校';
            if (id !== 'all') {
                const school = allSchools.find(s => s.id == id);
                if (school) {
                    selectedText = school.name;
                }
            }
            
            $('#schoolFilterInput').val(selectedText);
            $('#schoolDropdown').addClass('hidden');
            
            // 触发用户列表重新加载
            currentPage = 1;
            loadUsers();
        }
        
        function filterSchools() {
            const keyword = $('#schoolFilterInput').val().toLowerCase();
            
            // 如果输入为空，显示所有活跃学校
            if (!keyword || keyword === '全部学校') {
                renderSchoolDropdown(activeSchools);
                return;
            }
            
            // 匹配含有关键词的学校
            const filtered = activeSchools.filter(school => 
                school.name.toLowerCase().includes(keyword)
            );
            
            renderSchoolDropdown(filtered);
        }
        
        function renderSchoolDropdown(schools) {
            let html = '';
            
            if (schools.length === 0) {
                html = '<div class="no-results">没有匹配的学校</div>';
            } else {
                schools.forEach(school => {
                    const isSelected = $('#schoolFilter').val() == school.id;
                    html += `
                    <div class="dropdown-item ${isSelected ? 'selected' : ''}" onclick="selectSchool('${school.id}')">
                        ${school.name}
                    </div>`;
                });
            }
            
            $('#schoolDropdownItems').html(html);
        }
        
        // 单位下拉框相关函数
        function toggleCompanyDropdown() {
            $('#companyDropdown').toggleClass('hidden');
            $('#schoolDropdown').addClass('hidden'); // 关闭另一个下拉框
            
            // 当下拉框显示时，聚焦输入框
            if (!$('#companyDropdown').hasClass('hidden')) {
                $('#companyFilterInput').focus();
            }
        }
        
        function selectCompany(id) {
            $('#companyFilter').val(id);
            
            let selectedText = '全部单位';
            if (id !== 'all') {
                const company = allCompanies.find(c => c.id == id);
                if (company) {
                    selectedText = company.name;
                }
            }
            
            $('#companyFilterInput').val(selectedText);
            $('#companyDropdown').addClass('hidden');
            
            // 触发用户列表重新加载
            currentPage = 1;
            loadUsers();
        }
        
        function filterCompanies() {
            const keyword = $('#companyFilterInput').val().toLowerCase();
            
            // 如果输入为空，显示所有活跃单位
            if (!keyword || keyword === '全部单位') {
                renderCompanyDropdown(activeCompanies);
                return;
            }
            
            // 匹配含有关键词的单位
            const filtered = activeCompanies.filter(company => 
                company.name.toLowerCase().includes(keyword)
            );
            
            renderCompanyDropdown(filtered);
        }
        
        function renderCompanyDropdown(companies) {
            let html = '';
            
            if (companies.length === 0) {
                html = '<div class="no-results">没有匹配的单位</div>';
            } else {
                companies.forEach(company => {
                    const isSelected = $('#companyFilter').val() == company.id;
                    html += `
                    <div class="dropdown-item ${isSelected ? 'selected' : ''}" onclick="selectCompany('${company.id}')">
                        ${company.name}
                    </div>`;
                });
            }
            
            $('#companyDropdownItems').html(html);
        }

        // 添加窗口大小变化事件处理
        $(window).resize(function() {
            // 重新应用布局
            updateRoleFields();
        });

        // 初始化搜索下拉组件
        function initSearchDropdowns() {
            // 初始化学校搜索下拉框
            initSchoolSearchDropdown();
            
            // 初始化出版社搜索下拉框
            initPublisherSearchDropdown();
            
            // 初始化经销商搜索下拉框
            initDealerSearchDropdown();
        }

        // 更新基于角色显示不同的筛选组件
        function updateFiltersByRole(role) {
            // 隐藏所有筛选组件
            $('#schoolFilterContainer').addClass('hidden');
            $('#publisherFilterContainer').addClass('hidden');
            $('#dealerFilterContainer').addClass('hidden');
            
            // 根据角色显示对应的筛选组件
            if (role === 'all') {
                $('#schoolFilterContainer').removeClass('hidden');
                $('#publisherFilterContainer').removeClass('hidden');
                $('#dealerFilterContainer').removeClass('hidden');
            } else if (role === 'teacher') {
                $('#schoolFilterContainer').removeClass('hidden');
            } else if (role === 'publisher') {
                $('#publisherFilterContainer').removeClass('hidden');
            } else if (role === 'dealer') {
                $('#dealerFilterContainer').removeClass('hidden');
            }
        }

        // 初始化学校搜索下拉框
        function initSchoolSearchDropdown() {
            const searchInput = $('#schoolFilterInput');
            const dropdown = $('#schoolDropdown');
            const hiddenInput = $('#schoolFilter');
            
            // 初始设置
            searchInput.val('全部学校');
            hiddenInput.val('all');
            
            // 输入时过滤
            searchInput.on('input', function() {
                const query = $(this).val().toLowerCase();
                filterSchoolDropdown(query);
            });
            
            // 获得焦点时显示下拉菜单
            searchInput.on('focus', function() {
                dropdown.addClass('show');
                // 确保显示的是筛选后的结果
                filterSchoolDropdown($(this).val().toLowerCase());
            });
            
            // 点击文档其他地方关闭下拉菜单
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#schoolFilterContainer .search-dropdown').length) {
                    dropdown.removeClass('show');
                }
            });
            
            // 点击输入框阻止冒泡
            searchInput.on('click', function(e) {
                e.stopPropagation();
                dropdown.addClass('show');
            });
        }

        // 过滤学校下拉框
        function filterSchoolDropdown(query) {
            const dropdown = $('#schoolDropdown');
            
            // 清空下拉框，但保留"全部学校"选项
            dropdown.html('<div class="search-dropdown-item" data-id="all">全部学校</div>');
            
            // 如果查询为空，则显示所有活跃学校，但限制显示数量
            if (!query || query === '全部学校') {
                // 添加提示信息
                dropdown.append('<div class="search-dropdown-hint">请输入关键字搜索，或查看部分学校</div>');
                
                // 只显示前30所活跃学校
                const displaySchools = activeSchools.slice(0, 30);
                
                // 添加学校到下拉框
                displaySchools.forEach(school => {
                    dropdown.append(`<div class="search-dropdown-item" data-id="${school.id}">${school.name}</div>`);
                });
                
                // 如果学校数量超过了显示限制，显示提示信息
                if (activeSchools.length > 30) {
                    dropdown.append(`<div class="search-dropdown-more">显示 30/${activeSchools.length} 所学校，请输入关键字继续筛选</div>`);
                }
                
                // 如果没有学校数据，显示空提示
                if (activeSchools.length === 0) {
                    dropdown.append('<div class="search-dropdown-empty">没有可用的学校</div>');
                }
            } else {
                // 筛选匹配的学校
                const filtered = activeSchools.filter(school => 
                    school.name.toLowerCase().includes(query.toLowerCase())
                );
                
                // 显示筛选结果数量提示
                if (filtered.length > 0) {
                    dropdown.append(`<div class="search-dropdown-hint">找到 ${filtered.length} 所匹配的学校</div>`);
                }
                
                // 最多显示50条匹配结果
                const displayFiltered = filtered.slice(0, 50);
                
                // 添加筛选后的学校到下拉框
                displayFiltered.forEach(school => {
                    dropdown.append(`<div class="search-dropdown-item" data-id="${school.id}">${school.name}</div>`);
                });
                
                // 如果匹配结果超过显示限制，显示提示信息
                if (filtered.length > 50) {
                    dropdown.append(`<div class="search-dropdown-more">显示前 50/${filtered.length} 所匹配学校，请输入更多关键字继续筛选</div>`);
                }
                
                // 如果没有匹配的学校，显示空提示
                if (filtered.length === 0) {
                    dropdown.append('<div class="search-dropdown-empty">没有找到匹配的学校</div>');
                }
            }
            
            // 绑定选择事件
            dropdown.find('.search-dropdown-item').click(function() {
                const id = $(this).data('id');
                const text = $(this).text();
                
                $('#schoolFilterInput').val(text);
                $('#schoolFilter').val(id);
                dropdown.removeClass('show');
                
                // 重新加载用户列表
                currentPage = 1;
                loadUsers();
            });
            
            // 标记当前选中的项
            const currentValue = $('#schoolFilter').val();
            dropdown.find(`.search-dropdown-item[data-id="${currentValue}"]`).addClass('search-dropdown-selected');
        }

        // 初始化出版社搜索下拉框
        function initPublisherSearchDropdown() {
            const searchInput = $('#publisherFilterInput');
            const dropdown = $('#publisherDropdown');
            const hiddenInput = $('#publisherFilter');
            
            // 初始设置
            searchInput.val('全部出版社');
            hiddenInput.val('all');
            
            // 输入时过滤
            searchInput.on('input', function() {
                const query = $(this).val().toLowerCase();
                filterPublisherDropdown(query);
            });
            
            // 获得焦点时显示下拉菜单
            searchInput.on('focus', function() {
                dropdown.addClass('show');
                // 确保显示的是筛选后的结果
                filterPublisherDropdown($(this).val().toLowerCase());
            });
            
            // 点击文档其他地方关闭下拉菜单
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#publisherFilterContainer .search-dropdown').length) {
                    dropdown.removeClass('show');
                }
            });
            
            // 点击输入框阻止冒泡
            searchInput.on('click', function(e) {
                e.stopPropagation();
                dropdown.addClass('show');
            });
        }

        // 过滤出版社下拉框
        function filterPublisherDropdown(query) {
            const dropdown = $('#publisherDropdown');
            
            // 清空下拉框，但保留"全部出版社"选项
            dropdown.html('<div class="search-dropdown-item" data-id="all">全部出版社</div>');
            
            // 如果查询为空，则显示所有活跃出版社，但限制显示数量
            if (!query || query === '全部出版社') {
                // 添加提示信息
                dropdown.append('<div class="search-dropdown-hint">请输入关键字搜索，或查看部分出版社</div>');
                
                // 只显示前30家活跃出版社
                const displayPublishers = activePublishers.slice(0, 30);
                
                // 添加出版社到下拉框
                displayPublishers.forEach(publisher => {
                    dropdown.append(`<div class="search-dropdown-item" data-id="${publisher.id}">${publisher.name}</div>`);
                });
                
                // 如果出版社数量超过了显示限制，显示提示信息
                if (activePublishers.length > 30) {
                    dropdown.append(`<div class="search-dropdown-more">显示 30/${activePublishers.length} 家出版社，请输入关键字继续筛选</div>`);
                }
                
                // 如果没有出版社数据，显示空提示
                if (activePublishers.length === 0) {
                    dropdown.append('<div class="search-dropdown-empty">没有可用的出版社</div>');
                }
            } else {
                // 筛选匹配的出版社
                const filtered = activePublishers.filter(publisher => 
                    publisher.name.toLowerCase().includes(query.toLowerCase())
                );
                
                // 显示筛选结果数量提示
                if (filtered.length > 0) {
                    dropdown.append(`<div class="search-dropdown-hint">找到 ${filtered.length} 家匹配的出版社</div>`);
                }
                
                // 最多显示50条匹配结果
                const displayFiltered = filtered.slice(0, 50);
                
                // 添加筛选后的出版社到下拉框
                displayFiltered.forEach(publisher => {
                    dropdown.append(`<div class="search-dropdown-item" data-id="${publisher.id}">${publisher.name}</div>`);
                });
                
                // 如果匹配结果超过显示限制，显示提示信息
                if (filtered.length > 50) {
                    dropdown.append(`<div class="search-dropdown-more">显示前 50/${filtered.length} 家匹配出版社，请输入更多关键字继续筛选</div>`);
                }
                
                // 如果没有匹配的出版社，显示空提示
                if (filtered.length === 0) {
                    dropdown.append('<div class="search-dropdown-empty">没有找到匹配的出版社</div>');
                }
            }
            
            // 绑定选择事件
            dropdown.find('.search-dropdown-item').click(function() {
                const id = $(this).data('id');
                const text = $(this).text();
                
                $('#publisherFilterInput').val(text);
                $('#publisherFilter').val(id);
                dropdown.removeClass('show');
                
                // 重新加载用户列表
                currentPage = 1;
                loadUsers();
            });
            
            // 标记当前选中的项
            const currentValue = $('#publisherFilter').val();
            dropdown.find(`.search-dropdown-item[data-id="${currentValue}"]`).addClass('search-dropdown-selected');
        }

        // 初始化经销商搜索下拉框
        function initDealerSearchDropdown() {
            const searchInput = $('#dealerFilterInput');
            const dropdown = $('#dealerDropdown');
            const hiddenInput = $('#dealerFilter');
            
            // 初始设置
            searchInput.val('全部经销商');
            hiddenInput.val('all');
            
            // 输入时过滤
            searchInput.on('input', function() {
                const query = $(this).val().toLowerCase();
                filterDealerDropdown(query);
            });
            
            // 获得焦点时显示下拉菜单
            searchInput.on('focus', function() {
                dropdown.addClass('show');
                // 确保显示的是筛选后的结果
                filterDealerDropdown($(this).val().toLowerCase());
            });
            
            // 点击文档其他地方关闭下拉菜单
            $(document).on('click', function(e) {
                if (!$(e.target).closest('#dealerFilterContainer .search-dropdown').length) {
                    dropdown.removeClass('show');
                }
            });
            
            // 点击输入框阻止冒泡
            searchInput.on('click', function(e) {
                e.stopPropagation();
                dropdown.addClass('show');
            });
        }

        // 过滤经销商下拉框
        function filterDealerDropdown(query) {
            const dropdown = $('#dealerDropdown');
            
            // 清空下拉框，但保留"全部经销商"选项
            dropdown.html('<div class="search-dropdown-item" data-id="all">全部经销商</div>');
            
            // 如果查询为空，则显示所有活跃经销商，但限制显示数量
            if (!query || query === '全部经销商') {
                // 添加提示信息
                dropdown.append('<div class="search-dropdown-hint">请输入关键字搜索，或查看部分经销商</div>');
                
                // 只显示前30家活跃经销商
                const displayDealers = activeDealers.slice(0, 30);
                
                // 添加经销商到下拉框
                displayDealers.forEach(dealer => {
                    dropdown.append(`<div class="search-dropdown-item" data-id="${dealer.id}">${dealer.name}</div>`);
                });
                
                // 如果经销商数量超过了显示限制，显示提示信息
                if (activeDealers.length > 30) {
                    dropdown.append(`<div class="search-dropdown-more">显示 30/${activeDealers.length} 家经销商，请输入关键字继续筛选</div>`);
                }
                
                // 如果没有经销商数据，显示空提示
                if (activeDealers.length === 0) {
                    dropdown.append('<div class="search-dropdown-empty">没有可用的经销商</div>');
                }
            } else {
                // 筛选匹配的经销商
                const filtered = activeDealers.filter(dealer => 
                    dealer.name.toLowerCase().includes(query.toLowerCase())
                );
                
                // 显示筛选结果数量提示
                if (filtered.length > 0) {
                    dropdown.append(`<div class="search-dropdown-hint">找到 ${filtered.length} 家匹配的经销商</div>`);
                }
                
                // 最多显示50条匹配结果
                const displayFiltered = filtered.slice(0, 50);
                
                // 添加筛选后的经销商到下拉框
                displayFiltered.forEach(dealer => {
                    dropdown.append(`<div class="search-dropdown-item" data-id="${dealer.id}">${dealer.name}</div>`);
                });
                
                // 如果匹配结果超过显示限制，显示提示信息
                if (filtered.length > 50) {
                    dropdown.append(`<div class="search-dropdown-more">显示前 50/${filtered.length} 家匹配经销商，请输入更多关键字继续筛选</div>`);
                }
                
                // 如果没有匹配的经销商，显示空提示
                if (filtered.length === 0) {
                    dropdown.append('<div class="search-dropdown-empty">没有找到匹配的经销商</div>');
                }
            }
            
            // 绑定选择事件
            dropdown.find('.search-dropdown-item').click(function() {
                const id = $(this).data('id');
                const text = $(this).text();
                
                $('#dealerFilterInput').val(text);
                $('#dealerFilter').val(id);
                dropdown.removeClass('show');
                
                // 重新加载用户列表
                currentPage = 1;
                loadUsers();
            });
            
            // 标记当前选中的项
            const currentValue = $('#dealerFilter').val();
            dropdown.find(`.search-dropdown-item[data-id="${currentValue}"]`).addClass('search-dropdown-selected');
        }

        // 修改用户表单的搜索下拉框
        function initFormSearchDropdowns() {
            // 初始化学校搜索下拉框
            initFormSchoolDropdown();
            
            // 初始化出版社搜索下拉框
            initFormPublisherDropdown();
            
            // 初始化经销商搜索下拉框
            initFormDealerDropdown();
        }

        // 初始化学校搜索下拉框
        function initFormSchoolDropdown() {
            const searchInput = $('#schoolFormSearch');
            const dropdown = $('#schoolFormDropdown');
            const hiddenInput = $('#schoolId');
            
            // 重置页码
            dropdown.data('page', 1);
            
            // 输入时过滤
            searchInput.off('input').on('input', function() {
                    const query = $(this).val().toLowerCase();
                filterFormSchoolDropdown(query);
            });
            
            // 获得焦点时显示下拉菜单
            searchInput.off('focus').on('focus', function() {
                dropdown.addClass('show');
                // 重置页码
                dropdown.data('page', 1);
                // 确保显示的是筛选后的结果
                filterFormSchoolDropdown($(this).val().toLowerCase());
                });
                
                // 点击文档其他地方关闭下拉菜单
            $(document).off('click.schoolForm').on('click.schoolForm', function(e) {
                if (!$(e.target).closest('#schoolFormContainer').length) {
                    dropdown.removeClass('show');
                }
            });
            
            // 点击输入框阻止冒泡
            searchInput.off('click').on('click', function(e) {
                e.stopPropagation();
                dropdown.addClass('show');
                // 重置页码
                dropdown.data('page', 1);
                filterFormSchoolDropdown($(this).val().toLowerCase());
            });
        }

        // 过滤学校表单下拉框
        function filterFormSchoolDropdown(query) {
            console.log('过滤学校下拉框，查询:', query, '学校数据:', allSchools.length);
            const dropdown = $('#schoolFormDropdown');
            
            // 清空下拉框
            dropdown.empty();

            // 添加隐藏的数据属性来跟踪当前页码和每页数量
            if (!dropdown.data('page')) {
                dropdown.data('page', 1);
            }
            
            // 确保页码和每页数量是有效数字
            const currentPage = parseInt(dropdown.data('page')) || 1; 
            const pageSize = 50; // 固定每页显示50条
            
            // 保存更新后的页码
            dropdown.data('page', currentPage);
            
            if (!allSchools || !Array.isArray(allSchools) || allSchools.length === 0) {
                console.warn('没有学校数据可用');
                dropdown.append('<div class="search-dropdown-empty">没有可用的学校数据</div>');
                return;
            }
            
            // 如果查询为空，则显示所有学校 - 但限制最大显示数量
            if (!query) {
                // 添加提示信息
                dropdown.append('<div class="search-dropdown-hint">请输入关键字搜索，或浏览学校列表</div>');
                
                // 计算显示范围
                const startIndex = 0;
                const endIndex = currentPage * pageSize;
                const displaySchools = allSchools.slice(startIndex, endIndex);
                
                console.log(`显示学校: ${startIndex}-${endIndex}/${allSchools.length}, 页码: ${currentPage}, 每页数量: ${pageSize}`);
                
                // 添加学校到下拉框
                displaySchools.forEach(school => {
                    dropdown.append(`<div class="search-dropdown-item" data-id="${school.id}">${school.name}</div>`);
                });
                
                // 如果学校数量超过了当前显示数量，显示"加载更多"按钮
                if (allSchools.length > endIndex) {
                    dropdown.append(`<div class="search-dropdown-more load-more-btn" data-action="loadMoreSchools">已显示 ${endIndex}/${allSchools.length} 所学校，点击加载更多</div>`);
                }
            } else {
            // 筛选匹配的学校
            const filtered = allSchools.filter(school => 
                    school.name.toLowerCase().includes(query.toLowerCase())
                );
                
                // 显示筛选结果数量提示
                if (filtered.length > 0) {
                    dropdown.append(`<div class="search-dropdown-hint">找到 ${filtered.length} 所匹配的学校</div>`);
                    
                    // 计算显示范围
                    const startIndex = 0;
                    const endIndex = currentPage * pageSize;
                    const displayFiltered = filtered.slice(startIndex, endIndex);
                    
                    console.log(`显示筛选学校: ${startIndex}-${endIndex}/${filtered.length}, 页码: ${currentPage}, 每页数量: ${pageSize}`);
            
            // 添加筛选后的学校到下拉框
                    displayFiltered.forEach(school => {
                dropdown.append(`<div class="search-dropdown-item" data-id="${school.id}">${school.name}</div>`);
            });
            
                    // 如果筛选结果数量超过了当前显示数量，显示"加载更多"按钮
                    if (filtered.length > endIndex) {
                        dropdown.append(`<div class="search-dropdown-more load-more-btn" data-action="loadMoreFilteredSchools" data-query="${query}">已显示 ${endIndex}/${filtered.length} 所匹配学校，点击加载更多</div>`);
                    }
                } else {
            // 如果没有匹配的学校，显示空提示
                dropdown.append('<div class="search-dropdown-empty">没有找到匹配的学校</div>');
                }
            }
            
            // 重新绑定选择事件
            dropdown.find('.search-dropdown-item').off('click').on('click', function() {
                const id = $(this).data('id');
                const text = $(this).text();
                
                $('#schoolFormSearch').val(text);
                $('#schoolId').val(id);
                dropdown.removeClass('show');
            });
            
            // 绑定加载更多按钮的点击事件
            dropdown.find('.load-more-btn').off('click').on('click', function(e) {
                // 阻止事件冒泡，避免下拉框关闭
                e.stopPropagation();
                
                const action = $(this).data('action');
                if (action === 'loadMoreSchools') {
                    loadMoreSchools();
                } else if (action === 'loadMoreFilteredSchools') {
                    const query = $(this).data('query');
                    loadMoreFilteredSchools(query);
                }
            });
            
            // 标记当前选中的项
            const currentValue = $('#schoolId').val();
            if (currentValue) {
            dropdown.find(`.search-dropdown-item[data-id="${currentValue}"]`).addClass('search-dropdown-selected');
            }
        }

        // 加载更多学校
        function loadMoreSchools() {
            const dropdown = $('#schoolFormDropdown');
            const currentPage = dropdown.data('page') || 1;
            dropdown.data('page', currentPage + 1);
            filterFormSchoolDropdown('');
        }

        // 加载更多筛选后的学校
        function loadMoreFilteredSchools(query) {
            const dropdown = $('#schoolFormDropdown');
            const currentPage = dropdown.data('page') || 1;
            dropdown.data('page', currentPage + 1);
            filterFormSchoolDropdown(query);
        }

        // 初始化出版社搜索下拉框
        function initFormPublisherDropdown() {
            const searchInput = $('#publisherFormSearch');
            const dropdown = $('#publisherFormDropdown');
            const hiddenInput = $('#publisherId');
            
            // 重置页码
            dropdown.data('page', 1);
            
            // 输入时过滤
            searchInput.off('input').on('input', function() {
                const query = $(this).val().toLowerCase();
                filterFormPublisherDropdown(query);
            });
            
            // 获得焦点时显示下拉菜单
            searchInput.off('focus').on('focus', function() {
                dropdown.addClass('show');
                // 重置页码
                dropdown.data('page', 1);
                // 确保显示的是筛选后的结果
                filterFormPublisherDropdown($(this).val().toLowerCase());
            });
            
            // 点击文档其他地方关闭下拉菜单
            $(document).off('click.publisherForm').on('click.publisherForm', function(e) {
                if (!$(e.target).closest('#publisherFormContainer').length) {
                    dropdown.removeClass('show');
                }
            });
            
            // 点击输入框阻止冒泡
            searchInput.off('click').on('click', function(e) {
                e.stopPropagation();
                dropdown.addClass('show');
                // 重置页码
                dropdown.data('page', 1);
                filterFormPublisherDropdown($(this).val().toLowerCase());
            });
        }

        // 过滤出版社表单下拉框
        function filterFormPublisherDropdown(query) {
            console.log('过滤出版社下拉框，查询:', query);
            const dropdown = $('#publisherFormDropdown');
            
            // 清空下拉框
            dropdown.empty();

            // 添加隐藏的数据属性来跟踪当前页码和每页数量
            if (!dropdown.data('page')) {
                dropdown.data('page', 1);
            }
            
            // 确保页码和每页数量是有效数字
            const currentPage = parseInt(dropdown.data('page')) || 1;
            const pageSize = 50; // 固定每页显示50条
            
            // 保存更新后的页码
            dropdown.data('page', currentPage);
            
            // 获取所有出版社
            const publishers = Array.isArray(allCompanies) ? allCompanies.filter(company => company && company.type === 'publisher') : [];
            console.log(`找到 ${publishers.length} 家出版社`);
            
            if (publishers.length === 0) {
                console.warn('没有出版社数据可用');
                dropdown.append('<div class="search-dropdown-empty">没有可用的出版社数据</div>');
                return;
            }
            
            // 如果查询为空，则显示所有出版社 - 但限制最大显示数量
            if (!query) {
                // 添加提示信息
                dropdown.append('<div class="search-dropdown-hint">请输入关键字搜索，或浏览出版社列表</div>');
                
                // 计算显示范围
                const startIndex = 0;
                const endIndex = currentPage * pageSize;
                const displayPublishers = publishers.slice(startIndex, endIndex);
                
                console.log(`显示出版社: ${startIndex}-${endIndex}/${publishers.length}`);
                
                // 添加出版社到下拉框
                displayPublishers.forEach(publisher => {
                    dropdown.append(`<div class="search-dropdown-item" data-id="${publisher.id}">${publisher.name}</div>`);
                });
                
                // 如果出版社数量超过了当前显示数量，显示"加载更多"按钮
                if (publishers.length > endIndex) {
                    dropdown.append(`<div class="search-dropdown-more load-more-btn" data-action="loadMorePublishers">已显示 ${endIndex}/${publishers.length} 家出版社，点击加载更多</div>`);
                }
            } else {
            // 筛选匹配的出版社
                const filtered = publishers.filter(publisher => 
                    publisher.name.toLowerCase().includes(query.toLowerCase())
                );
                
                // 显示筛选结果数量提示
                if (filtered.length > 0) {
                    dropdown.append(`<div class="search-dropdown-hint">找到 ${filtered.length} 家匹配的出版社</div>`);
                    
                    // 计算显示范围
                    const startIndex = 0;
                    const endIndex = currentPage * pageSize;
                    const displayFiltered = filtered.slice(startIndex, endIndex);
                    
                    console.log(`显示筛选出版社: ${startIndex}-${endIndex}/${filtered.length}`);
            
            // 添加筛选后的出版社到下拉框
                    displayFiltered.forEach(publisher => {
                dropdown.append(`<div class="search-dropdown-item" data-id="${publisher.id}">${publisher.name}</div>`);
            });
            
                    // 如果筛选结果数量超过了当前显示数量，显示"加载更多"按钮
                    if (filtered.length > endIndex) {
                        dropdown.append(`<div class="search-dropdown-more load-more-btn" data-action="loadMoreFilteredPublishers" data-query="${query}">已显示 ${endIndex}/${filtered.length} 家匹配出版社，点击加载更多</div>`);
                    }
                } else {
            // 如果没有匹配的出版社，显示空提示
                dropdown.append('<div class="search-dropdown-empty">没有找到匹配的出版社</div>');
                }
            }
            
            // 重新绑定选择事件
            dropdown.find('.search-dropdown-item').off('click').on('click', function() {
                const id = $(this).data('id');
                const text = $(this).text();
                
                $('#publisherFormSearch').val(text);
                $('#publisherId').val(id);
                dropdown.removeClass('show');
            });
            
            // 绑定加载更多按钮的点击事件
            dropdown.find('.load-more-btn').off('click').on('click', function(e) {
                // 阻止事件冒泡，避免下拉框关闭
                e.stopPropagation();
                
                const action = $(this).data('action');
                if (action === 'loadMorePublishers') {
                    loadMorePublishers();
                } else if (action === 'loadMoreFilteredPublishers') {
                    const query = $(this).data('query');
                    loadMoreFilteredPublishers(query);
                }
            });
            
            // 标记当前选中的项
            const currentValue = $('#publisherId').val();
            if (currentValue) {
            dropdown.find(`.search-dropdown-item[data-id="${currentValue}"]`).addClass('search-dropdown-selected');
            }
        }

        // 加载更多出版社
        function loadMorePublishers() {
            const dropdown = $('#publisherFormDropdown');
            const currentPage = dropdown.data('page') || 1;
            dropdown.data('page', currentPage + 1);
            filterFormPublisherDropdown('');
        }

        // 加载更多筛选后的出版社
        function loadMoreFilteredPublishers(query) {
            const dropdown = $('#publisherFormDropdown');
            const currentPage = dropdown.data('page') || 1;
            dropdown.data('page', currentPage + 1);
            filterFormPublisherDropdown(query);
        }

        // 初始化经销商搜索下拉框
        function initFormDealerDropdown() {
            const searchInput = $('#dealerFormSearch');
            const dropdown = $('#dealerFormDropdown');
            const hiddenInput = $('#dealerId');
            
            // 重置页码
            dropdown.data('page', 1);
            
            // 输入时过滤
            searchInput.off('input').on('input', function() {
                const query = $(this).val().toLowerCase();
                filterFormDealerDropdown(query);
            });
            
            // 获得焦点时显示下拉菜单
            searchInput.off('focus').on('focus', function() {
            dropdown.addClass('show');
                // 重置页码
                dropdown.data('page', 1);
                // 确保显示的是筛选后的结果
                filterFormDealerDropdown($(this).val().toLowerCase());
            });
            
            // 点击文档其他地方关闭下拉菜单
            $(document).off('click.dealerForm').on('click.dealerForm', function(e) {
                if (!$(e.target).closest('#dealerFormContainer').length) {
                    dropdown.removeClass('show');
                }
            });
            
            // 点击输入框阻止冒泡
            searchInput.off('click').on('click', function(e) {
                e.stopPropagation();
                dropdown.addClass('show');
                // 重置页码
                dropdown.data('page', 1);
                filterFormDealerDropdown($(this).val().toLowerCase());
            });
        }

        // 过滤经销商表单下拉框
        function filterFormDealerDropdown(query) {
            console.log('过滤经销商下拉框，查询:', query);
            const dropdown = $('#dealerFormDropdown');
            
            // 清空下拉框
            dropdown.empty();
            
            // 添加隐藏的数据属性来跟踪当前页码和每页数量
            if (!dropdown.data('page')) {
                dropdown.data('page', 1);
            }
            
            // 确保页码和每页数量是有效数字
            const currentPage = parseInt(dropdown.data('page')) || 1;
            const pageSize = 50; // 固定每页显示50条
            
            // 保存更新后的页码
            dropdown.data('page', currentPage);
            
            // 获取所有经销商
            const dealers = Array.isArray(allCompanies) ? allCompanies.filter(company => company && company.type === 'dealer') : [];
            console.log(`找到 ${dealers.length} 家经销商`);
            
            if (dealers.length === 0) {
                console.warn('没有经销商数据可用');
                dropdown.append('<div class="search-dropdown-empty">没有可用的经销商数据</div>');
                return;
            }
            
            // 如果查询为空，则显示所有经销商 - 但限制最大显示数量
            if (!query) {
                // 添加提示信息
                dropdown.append('<div class="search-dropdown-hint">请输入关键字搜索，或浏览经销商列表</div>');
                
                // 计算显示范围
                const startIndex = 0;
                const endIndex = currentPage * pageSize;
                const displayDealers = dealers.slice(startIndex, endIndex);
                
                console.log(`显示经销商: ${startIndex}-${endIndex}/${dealers.length}, 页码: ${currentPage}, 每页数量: ${pageSize}`);
                
                // 添加经销商到下拉框
                displayDealers.forEach(dealer => {
                    dropdown.append(`<div class="search-dropdown-item" data-id="${dealer.id}">${dealer.name}</div>`);
                });
                
                // 如果经销商数量超过了当前显示数量，显示"加载更多"按钮
                if (dealers.length > endIndex) {
                    dropdown.append(`<div class="search-dropdown-more load-more-btn" data-action="loadMoreDealers">已显示 ${endIndex}/${dealers.length} 家经销商，点击加载更多</div>`);
                }
            } else {
            // 筛选匹配的经销商
                const filtered = dealers.filter(dealer => 
                    dealer.name.toLowerCase().includes(query.toLowerCase())
                );
                
                // 显示筛选结果数量提示
                if (filtered.length > 0) {
                    dropdown.append(`<div class="search-dropdown-hint">找到 ${filtered.length} 家匹配的经销商</div>`);
                    
                    // 计算显示范围
                    const startIndex = 0;
                    const endIndex = currentPage * pageSize;
                    const displayFiltered = filtered.slice(startIndex, endIndex);
                    
                    console.log(`显示筛选经销商: ${startIndex}-${endIndex}/${filtered.length}, 页码: ${currentPage}, 每页数量: ${pageSize}`);
            
            // 添加筛选后的经销商到下拉框
                    displayFiltered.forEach(dealer => {
                dropdown.append(`<div class="search-dropdown-item" data-id="${dealer.id}">${dealer.name}</div>`);
            });
            
                    // 如果筛选结果数量超过了当前显示数量，显示"加载更多"按钮
                    if (filtered.length > endIndex) {
                        dropdown.append(`<div class="search-dropdown-more load-more-btn" data-action="loadMoreFilteredDealers" data-query="${query}">已显示 ${endIndex}/${filtered.length} 家匹配经销商，点击加载更多</div>`);
                    }
                } else {
            // 如果没有匹配的经销商，显示空提示
                dropdown.append('<div class="search-dropdown-empty">没有找到匹配的经销商</div>');
                }
            }
            
            // 重新绑定选择事件
            dropdown.find('.search-dropdown-item').off('click').on('click', function() {
                const id = $(this).data('id');
                const text = $(this).text();
                
                $('#dealerFormSearch').val(text);
                $('#dealerId').val(id);
                dropdown.removeClass('show');
            });
            
            // 绑定加载更多按钮的点击事件
            dropdown.find('.load-more-btn').off('click').on('click', function(e) {
                // 阻止事件冒泡，避免下拉框关闭
                e.stopPropagation();
                
                const action = $(this).data('action');
                if (action === 'loadMoreDealers') {
                    loadMoreDealers();
                } else if (action === 'loadMoreFilteredDealers') {
                    const query = $(this).data('query');
                    loadMoreFilteredDealers(query);
                }
            });
            
            // 标记当前选中的项
            const currentValue = $('#dealerId').val();
            if (currentValue) {
            dropdown.find(`.search-dropdown-item[data-id="${currentValue}"]`).addClass('search-dropdown-selected');
            }
        }

        // 加载更多经销商
        function loadMoreDealers() {
            const dropdown = $('#dealerFormDropdown');
            const currentPage = dropdown.data('page') || 1;
            dropdown.data('page', currentPage + 1);
            filterFormDealerDropdown('');
        }

        // 加载更多筛选后的经销商
        function loadMoreFilteredDealers(query) {
            const dropdown = $('#dealerFormDropdown');
            const currentPage = dropdown.data('page') || 1;
            dropdown.data('page', currentPage + 1);
            filterFormDealerDropdown(query);
        }

        // 添加根据角色更新筛选器显示的函数
        function updateFiltersByRole(role) {
            // 隐藏所有筛选器
            $('#schoolFilterContainer').addClass('hidden');
            $('#publisherFilterContainer').addClass('hidden');
            $('#dealerFilterContainer').addClass('hidden');
            
            // 根据角色显示对应的筛选器
            if (role === 'all') {
                // 全部角色时，显示所有筛选器
                $('#schoolFilterContainer').removeClass('hidden');
                $('#publisherFilterContainer').removeClass('hidden');
                $('#dealerFilterContainer').removeClass('hidden');
            } else if (role === 'teacher') {
                // 教师角色只显示学校筛选
                $('#schoolFilterContainer').removeClass('hidden');
            } else if (role === 'publisher') {
                // 出版社角色只显示出版社筛选
                $('#publisherFilterContainer').removeClass('hidden');
            } else if (role === 'dealer') {
                // 经销商角色只显示经销商筛选
                $('#dealerFilterContainer').removeClass('hidden');
            }
        }

        // 添加渲染出版社下拉框的函数
        function renderPublisherDropdown(publishers) {
            let html = '';
            
            if (publishers.length === 0) {
                html = '<div class="no-results">没有匹配的出版社</div>';
            } else {
                publishers.forEach(publisher => {
                    const isSelected = $('#publisherFilter').val() == publisher.id;
                    html += `
                    <div class="dropdown-item ${isSelected ? 'selected' : ''}" onclick="selectPublisher('${publisher.id}')">
                        ${publisher.name}
                    </div>`;
                });
            }
            
            $('#publisherDropdown').html(html);
        }

        // 添加出版社选择函数
        function selectPublisher(id) {
            $('#publisherFilter').val(id);
            
            let selectedText = '全部出版社';
            if (id !== 'all') {
                const publisher = activePublishers.find(p => p.id == id);
                if (publisher) {
                    selectedText = publisher.name;
                }
            }
            
            $('#publisherFilterInput').val(selectedText);
            $('#publisherDropdown').removeClass('show');
            
            // 触发用户列表重新加载
            currentPage = 1;
            loadUsers();
        }

        // 添加渲染经销商下拉框的函数
        function renderDealerDropdown(dealers) {
            let html = '';
            
            if (dealers.length === 0) {
                html = '<div class="no-results">没有匹配的经销商</div>';
            } else {
                dealers.forEach(dealer => {
                    const isSelected = $('#dealerFilter').val() == dealer.id;
                    html += `
                    <div class="dropdown-item ${isSelected ? 'selected' : ''}" onclick="selectDealer('${dealer.id}')">
                        ${dealer.name}
                    </div>`;
                });
            }
            
            $('#dealerDropdown').html(html);
        }

        // 添加经销商选择函数
        function selectDealer(id) {
            $('#dealerFilter').val(id);
            
            let selectedText = '全部经销商';
            if (id !== 'all') {
                const dealer = activeDealers.find(d => d.id == id);
                if (dealer) {
                    selectedText = dealer.name;
                }
            }
            
            $('#dealerFilterInput').val(selectedText);
            $('#dealerDropdown').removeClass('show');
            
            // 触发用户列表重新加载
            currentPage = 1;
            loadUsers();
        }

        // 批量导入用户相关函数
        function updateFileInfo(input) {
            const fileInfo = document.getElementById('fileInfo');
            const fileName = document.getElementById('fileName');
            
            if (input.files && input.files[0]) {
                const file = input.files[0];
                fileInfo.classList.remove('hidden');
                fileName.textContent = `文件名: ${file.name} (${formatFileSize(file.size)})`;
            } else {
                fileInfo.classList.add('hidden');
                fileName.textContent = '未选择文件';
            }
        }
        
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
            else return (bytes / 1048576).toFixed(2) + ' MB';
        }
        
        function showImportProgress(visible) {
            const progressElement = document.getElementById('importProgress');
            if (visible) {
                progressElement.classList.remove('hidden');
            } else {
                progressElement.classList.add('hidden');
            }
        }
        
        function updateProgressBar(percent) {
            const progressBar = document.getElementById('progressBar');
            const progressPercent = document.getElementById('progressPercent');
            
            progressBar.style.width = percent + '%';
            progressPercent.textContent = percent + '%';
        }
        
        function showImportResults(visible, html = '') {
            const resultsElement = document.getElementById('importResults');
            const detailsElement = document.getElementById('importResultDetails');
            
            if (visible) {
                resultsElement.classList.remove('hidden');
                if (html) {
                    detailsElement.innerHTML = html;
                }
            } else {
                resultsElement.classList.add('hidden');
                detailsElement.innerHTML = '';
            }
        }
        
        function importUsers() {
            const fileInput = document.getElementById('importFile');
            if (!fileInput.files || !fileInput.files[0]) {
                showErrorModal('请选择Excel文件');
                return;
            }
            
            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('file', file);
            
            // 显示进度条
            showImportProgress(true);
            updateProgressBar(0);
            showImportResults(false);
            
            // 禁用导入按钮
            $('#startImportBtn').prop('disabled', true).text('导入中...').addClass('opacity-70');
            
            // 发送上传请求
            $.ajax({
                url: '/api/admin/import_users',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    // 创建上传进度监控
                    const xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener('progress', function(evt) {
                        if (evt.lengthComputable) {
                            const percentComplete = Math.round((evt.loaded / evt.total) * 100);
                            updateProgressBar(percentComplete);
                        }
                    }, false);
                    return xhr;
                },
                success: function(response) {
                    // 完成上传，更新进度为100%
                    updateProgressBar(100);
                    
                    // 还原导入按钮状态
                    $('#startImportBtn').prop('disabled', false).text('开始导入').removeClass('opacity-70');
                    
                    // 处理响应
                    if (response.code === 0) {
                        const result = response.data;
                        const successCount = result.success_count || 0;
                        const errorCount = result.error_count || 0;
                        const warnings = result.warnings || [];
                        const errors = result.errors || [];
                        
                        // 构建结果HTML
                        let resultHtml = `
                            <div class="mb-3">
                                <div class="flex items-center">
                                    <span class="inline-flex items-center justify-center w-6 h-6 mr-2 bg-green-100 text-green-600 rounded-full"><i class="fas fa-check"></i></span>
                                    <span class="font-medium">成功导入: ${successCount} 条记录</span>
                                </div>
                                <div class="flex items-center mt-1">
                                    <span class="inline-flex items-center justify-center w-6 h-6 mr-2 bg-red-100 text-red-600 rounded-full"><i class="fas fa-times"></i></span>
                                    <span class="font-medium">导入失败: ${errorCount} 条记录</span>
                                </div>
                            </div>
                        `;
                        
                        // 添加警告信息
                        if (warnings.length > 0) {
                            resultHtml += '<div class="mb-3"><p class="font-medium text-yellow-600 mb-1">警告信息:</p><ul class="list-disc list-inside text-yellow-600 pl-2">';
                            warnings.forEach(warning => {
                                resultHtml += `<li>${warning}</li>`;
                            });
                            resultHtml += '</ul></div>';
                        }
                        
                        // 添加错误信息
                        if (errors.length > 0) {
                            resultHtml += '<div><p class="font-medium text-red-600 mb-1">错误信息:</p><ul class="list-disc list-inside text-red-600 pl-2">';
                            errors.forEach(error => {
                                if (typeof error === 'object' && error !== null) {
                                    // 处理错误对象
                                    const rowNum = error.row || '';
                                    const username = error.username || '';
                                    
                                    if (Array.isArray(error.errors)) {
                                        // 多个错误消息
                                        error.errors.forEach(err => {
                                            resultHtml += `<li>行 ${rowNum}${username ? ' (' + username + ')' : ''}: ${err}</li>`;
                                        });
                                    } else if (error.error) {
                                        // 单个错误消息
                                        resultHtml += `<li>行 ${rowNum}${username ? ' (' + username + ')' : ''}: ${error.error}</li>`;
                                    } else {
                                        // 未知结构，尝试转换为字符串
                                        resultHtml += `<li>行 ${rowNum}${username ? ' (' + username + ')' : ''}: ${JSON.stringify(error)}</li>`;
                                    }
                                } else {
                                    // 字符串或其他类型的错误
                                    resultHtml += `<li>${error}</li>`;
                                }
                            });
                            resultHtml += '</ul></div>';
                        }
                        
                        // 显示结果
                        showImportResults(true, resultHtml);
                        
                        // 如果有成功导入的记录，3秒后刷新用户列表
                        if (successCount > 0) {
                            setTimeout(function() {
                                loadUsers();
                            }, 3000);
                        }
                    } else {
                        // 显示错误信息
                        const errorMessage = response.message || '导入失败，请确认文件格式正确';
                        showImportResults(true, `<div class="text-red-600">${errorMessage}</div>`);
                    }
                },
                error: function(xhr) {
                    // 还原导入按钮状态
                    $('#startImportBtn').prop('disabled', false).text('开始导入').removeClass('opacity-70');
                    
                    // 处理错误
                    let errorMessage = '网络错误，请稍后重试';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    
                    // 显示错误信息
                    showImportResults(true, `<div class="text-red-600">${errorMessage}</div>`);
                }
            });
        }
    </script>
</body>
</html> 