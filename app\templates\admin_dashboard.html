<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员首页 - 数据统计</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script defer src="/static/js/alpine.min.js"></script>
    <script src="/static/jquery.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 现代化设计样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .chart-container {
            height: 300px;
            position: relative;
        }

        .loading-skeleton {
            background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 数字动画 */
        .counter {
            font-variant-numeric: tabular-nums;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .chart-container {
                height: 250px;
            }
        }
    </style>
</head>
<<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen" x-data="dashboardManager()">
    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <div class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
        <!-- 顶部控制栏 -->
        <div class="bg-white/70 backdrop-blur-xl border-b border-white/20 sticky top-0 z-40">
            <div class="container mx-auto px-6 py-4">
                <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center">
                    <!-- 日期选择器 -->
                    <div class="flex items-center space-x-4 bg-white/60 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/30 shadow-lg">
                        <div class="flex items-center space-x-2">
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                            <span class="text-slate-700 font-medium">统计时间</span>
                        </div>
                        <input type="date" x-model="dateRange.start" class="border-0 bg-transparent text-sm focus:outline-none text-slate-700">
                        <span class="text-slate-400">—</span>
                        <input type="date" x-model="dateRange.end" class="border-0 bg-transparent text-sm focus:outline-none text-slate-700">
                        <button @click="applyDateFilter()" class="btn-primary text-white px-4 py-2 rounded-xl text-sm shadow-md hover:shadow-lg transition-all">
                            <i class="fas fa-search mr-1"></i>查询
                        </button>
                    </div>

                    <!-- 刷新按钮 -->
                    <button @click="refreshData()"
                            class="btn-primary text-white px-6 py-3 rounded-2xl flex items-center space-x-2 shadow-lg hover:shadow-xl transition-all mt-4 lg:mt-0">
                        <i class="fas fa-sync-alt" :class="{'animate-spin': loading}"></i>
                        <span>实时刷新</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="container mx-auto px-6 py-8">

            <!-- 核心指标仪表板 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- 用户统计 -->
                <div class="group relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-400 to-blue-600 rounded-3xl transform rotate-1 group-hover:rotate-2 transition-transform duration-300"></div>
                    <div class="relative bg-white rounded-3xl p-6 shadow-xl border border-white/20">
                        <div class="flex items-center justify-between mb-4">
                            <div class="bg-gradient-to-br from-blue-100 to-blue-200 p-4 rounded-2xl">
                                <i class="fas fa-users text-blue-600 text-2xl"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent counter" x-text="stats.user_stats?.total_users || 0"></div>
                                <div class="text-sm text-slate-500 font-medium">总用户数</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-slate-600 text-sm">新增用户</span>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-arrow-up text-green-500 text-xs"></i>
                                <span class="text-green-600 font-semibold text-sm" x-text="stats.user_stats?.new_users || 0"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 样书统计 -->
                <div class="group relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-emerald-400 to-emerald-600 rounded-3xl transform -rotate-1 group-hover:-rotate-2 transition-transform duration-300"></div>
                    <div class="relative bg-white rounded-3xl p-6 shadow-xl border border-white/20">
                        <div class="flex items-center justify-between mb-4">
                            <div class="bg-gradient-to-br from-emerald-100 to-emerald-200 p-4 rounded-2xl">
                                <i class="fas fa-book text-emerald-600 text-2xl"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-emerald-800 bg-clip-text text-transparent counter" x-text="stats.book_stats?.total_books || 0"></div>
                                <div class="text-sm text-slate-500 font-medium">样书总数</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-slate-600 text-sm">新增样书</span>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-arrow-up text-green-500 text-xs"></i>
                                <span class="text-green-600 font-semibold text-sm" x-text="stats.book_stats?.new_books || 0"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 订单统计 -->
                <div class="group relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-400 to-purple-600 rounded-3xl transform rotate-1 group-hover:rotate-2 transition-transform duration-300"></div>
                    <div class="relative bg-white rounded-3xl p-6 shadow-xl border border-white/20">
                        <div class="flex items-center justify-between mb-4">
                            <div class="bg-gradient-to-br from-purple-100 to-purple-200 p-4 rounded-2xl">
                                <i class="fas fa-shopping-cart text-purple-600 text-2xl"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-purple-800 bg-clip-text text-transparent counter" x-text="stats.order_stats?.total_orders || 0"></div>
                                <div class="text-sm text-slate-500 font-medium">订单总数</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-slate-600 text-sm">新增订单</span>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-arrow-up text-green-500 text-xs"></i>
                                <span class="text-green-600 font-semibold text-sm" x-text="stats.order_stats?.new_orders || 0"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 财务统计 -->
                <div class="group relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-amber-400 to-amber-600 rounded-3xl transform -rotate-1 group-hover:-rotate-2 transition-transform duration-300"></div>
                    <div class="relative bg-white rounded-3xl p-6 shadow-xl border border-white/20">
                        <div class="flex items-center justify-between mb-4">
                            <div class="bg-gradient-to-br from-amber-100 to-amber-200 p-4 rounded-2xl">
                                <i class="fas fa-chart-line text-amber-600 text-2xl"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold bg-gradient-to-r from-amber-600 to-amber-800 bg-clip-text text-transparent counter" x-text="formatCurrency(stats.financial_stats?.total_revenue || 0)"></div>
                                <div class="text-sm text-slate-500 font-medium">总交易额</div>
                            </div>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-slate-600 text-sm">本期交易</span>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-arrow-up text-green-500 text-xs"></i>
                                <span class="text-green-600 font-semibold text-sm" x-text="formatCurrency(stats.financial_stats?.period_revenue || 0)"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 高级数据可视化区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8 mb-8">
                <!-- 用户角色分布 -->
                <div class="group relative">
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-indigo-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                    <div class="relative bg-white/80 backdrop-blur-xl rounded-3xl p-8 border border-white/30 shadow-2xl">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                                <h3 class="text-xl font-bold text-slate-800">用户角色分布</h3>
                            </div>
                            <i class="fas fa-users text-blue-500 text-xl"></i>
                        </div>
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="text-center p-3 bg-blue-50/50 rounded-2xl">
                                <div class="text-2xl font-bold text-blue-600" x-text="stats.user_stats?.role_distribution?.teacher || 0"></div>
                                <div class="text-xs text-slate-500 font-medium">教师用户</div>
                            </div>
                            <div class="text-center p-3 bg-emerald-50/50 rounded-2xl">
                                <div class="text-2xl font-bold text-emerald-600" x-text="stats.user_stats?.role_distribution?.publisher || 0"></div>
                                <div class="text-xs text-slate-500 font-medium">出版社</div>
                            </div>
                            <div class="text-center p-3 bg-amber-50/50 rounded-2xl">
                                <div class="text-2xl font-bold text-amber-600" x-text="stats.user_stats?.role_distribution?.dealer || 0"></div>
                                <div class="text-xs text-slate-500 font-medium">经销商</div>
                            </div>
                            <div class="text-center p-3 bg-purple-50/50 rounded-2xl">
                                <div class="text-2xl font-bold text-purple-600" x-text="stats.user_stats?.role_distribution?.admin || 0"></div>
                                <div class="text-xs text-slate-500 font-medium">管理员</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="userChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 样书规划分布 -->
                <div class="group relative">
                    <div class="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-teal-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                    <div class="relative bg-white/80 backdrop-blur-xl rounded-3xl p-8 border border-white/30 shadow-2xl">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-emerald-500 rounded-full animate-pulse"></div>
                                <h3 class="text-xl font-bold text-slate-800">样书规划分布</h3>
                            </div>
                            <i class="fas fa-book text-emerald-500 text-xl"></i>
                        </div>
                        <div class="space-y-4 mb-6">
                            <div class="flex justify-between items-center p-3 bg-red-50/50 rounded-2xl">
                                <span class="text-slate-700 font-medium">国家规划</span>
                                <span class="text-2xl font-bold text-red-600" x-text="stats.book_stats?.regulation_distribution?.national || 0"></span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-blue-50/50 rounded-2xl">
                                <span class="text-slate-700 font-medium">省级规划</span>
                                <span class="text-2xl font-bold text-blue-600" x-text="stats.book_stats?.regulation_distribution?.provincial || 0"></span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gray-50/50 rounded-2xl">
                                <span class="text-slate-700 font-medium">一般教材</span>
                                <span class="text-2xl font-bold text-gray-600" x-text="stats.book_stats?.regulation_distribution?.general || 0"></span>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="bookChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 订单状态流转 -->
                <div class="group relative">
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-pink-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                    <div class="relative bg-white/80 backdrop-blur-xl rounded-3xl p-8 border border-white/30 shadow-2xl">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
                                <h3 class="text-xl font-bold text-slate-800">订单状态流转</h3>
                            </div>
                            <i class="fas fa-chart-pie text-purple-500 text-xl"></i>
                        </div>
                        <div class="space-y-4 mb-6">
                            <div class="flex justify-between items-center p-3 bg-yellow-50/50 rounded-2xl">
                                <span class="text-slate-700 font-medium">预结算</span>
                                <span class="text-2xl font-bold text-yellow-600" x-text="stats.order_stats?.status_distribution?.pre_settlement || 0"></span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-orange-50/50 rounded-2xl">
                                <span class="text-slate-700 font-medium">待支付</span>
                                <span class="text-2xl font-bold text-orange-600" x-text="stats.order_stats?.status_distribution?.pending_payment || 0"></span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-green-50/50 rounded-2xl">
                                <span class="text-slate-700 font-medium">已结算</span>
                                <span class="text-2xl font-bold text-green-600" x-text="stats.order_stats?.status_distribution?.settled || 0"></span>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="orderChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 高级业务分析 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- 系统活跃度趋势 -->
                <div class="group relative">
                    <div class="absolute inset-0 bg-gradient-to-br from-indigo-400/20 to-blue-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                    <div class="relative bg-white/80 backdrop-blur-xl rounded-3xl p-8 border border-white/30 shadow-2xl">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-indigo-500 rounded-full animate-pulse"></div>
                                <h3 class="text-xl font-bold text-slate-800">系统活跃度趋势</h3>
                            </div>
                            <i class="fas fa-chart-line text-indigo-500 text-xl"></i>
                        </div>
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="text-center p-4 bg-indigo-50/50 rounded-2xl">
                                <div class="text-3xl font-bold text-indigo-600" x-text="stats.activity_stats?.total_logs || 0"></div>
                                <div class="text-sm text-slate-500 font-medium">总操作数</div>
                            </div>
                            <div class="text-center p-4 bg-green-50/50 rounded-2xl">
                                <div class="text-3xl font-bold text-green-600" x-text="stats.user_stats?.active_users || 0"></div>
                                <div class="text-sm text-slate-500 font-medium">活跃用户</div>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="activityChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 样书申请处理效率 -->
                <div class="group relative">
                    <div class="absolute inset-0 bg-gradient-to-br from-rose-400/20 to-pink-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                    <div class="relative bg-white/80 backdrop-blur-xl rounded-3xl p-8 border border-white/30 shadow-2xl">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 bg-rose-500 rounded-full animate-pulse"></div>
                                <h3 class="text-xl font-bold text-slate-800">样书申请统计</h3>
                            </div>
                            <i class="fas fa-file-alt text-rose-500 text-xl"></i>
                        </div>
                        <div class="space-y-4 mb-6">
                            <div class="flex justify-between items-center p-4 bg-blue-50/50 rounded-2xl">
                                <span class="text-slate-700 font-medium">总申请数</span>
                                <span class="text-3xl font-bold text-blue-600" x-text="stats.request_stats?.total_requests || 0"></span>
                            </div>
                            <div class="flex justify-between items-center p-4 bg-green-50/50 rounded-2xl">
                                <span class="text-slate-700 font-medium">新增申请</span>
                                <span class="text-3xl font-bold text-green-600" x-text="stats.request_stats?.new_requests || 0"></span>
                            </div>
                            <div class="flex justify-between items-center p-4 bg-amber-50/50 rounded-2xl">
                                <span class="text-slate-700 font-medium">平均处理天数</span>
                                <span class="text-3xl font-bold text-amber-600" x-text="Math.round(stats.request_stats?.avg_process_days || 0)"></span>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="requestChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 地域分布热力图 -->
            <div class="group relative mb-8">
                <div class="absolute inset-0 bg-gradient-to-br from-emerald-400/20 to-teal-600/20 rounded-3xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                <div class="relative bg-white/80 backdrop-blur-xl rounded-3xl p-8 border border-white/30 shadow-2xl">
                    <div class="flex items-center justify-between mb-6">
                        <div class="flex items-center space-x-3">
                            <div class="w-3 h-3 bg-emerald-500 rounded-full animate-pulse"></div>
                            <h3 class="text-2xl font-bold text-slate-800">全国地域分布热力图</h3>
                        </div>
                        <i class="fas fa-map-marked-alt text-emerald-500 text-2xl"></i>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 max-h-96 overflow-y-auto custom-scrollbar">
                        <template x-for="(item, index) in (stats.regional_stats?.school_distribution || [])" :key="index">
                            <div class="group/item relative overflow-hidden">
                                <div class="absolute inset-0 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-2xl transform scale-0 group-hover/item:scale-100 transition-transform duration-300"></div>
                                <div class="relative bg-emerald-50/50 rounded-2xl p-4 border border-emerald-100 group-hover/item:border-white transition-all duration-300">
                                    <div class="flex justify-between items-center">
                                        <span class="text-slate-700 font-medium group-hover/item:text-white transition-colors" x-text="item.province"></span>
                                        <div class="flex items-center space-x-2">
                                            <span class="text-sm text-slate-500 group-hover/item:text-emerald-100 transition-colors">学校</span>
                                            <span class="text-2xl font-bold text-emerald-600 group-hover/item:text-white transition-colors" x-text="item.school_count"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息通知组件 -->
    <script>
        let messageId = 0;

        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');

            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' :
                type === 'error' ? 'border-red-500' :
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;

            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' :
                        type === 'error' ? 'text-red-500' :
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-circle' :
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})"
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;

            container.appendChild(messageEl);

            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);

            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }

        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
    </script>

    <script>
        function dashboardManager() {
            return {
                loading: false,
                stats: {},
                charts: {},
                dateRange: {
                    start: '',
                    end: ''
                },

                init() {
                    // 设置默认日期范围（最近30天）
                    const today = new Date();
                    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

                    this.dateRange.end = today.toISOString().split('T')[0];
                    this.dateRange.start = thirtyDaysAgo.toISOString().split('T')[0];

                    this.loadStatistics();
                },

                async loadStatistics() {
                    this.loading = true;
                    try {
                        const params = new URLSearchParams({
                            start_date: this.dateRange.start,
                            end_date: this.dateRange.end
                        });

                        const response = await fetch(`/api/admin_dashboard/get_dashboard_stats?${params}`);
                        const result = await response.json();

                        if (result.code === 0) {
                            this.stats = result.data;
                            this.updateCharts();
                            showMessage('数据加载成功', 'success');
                        } else {
                            showMessage('获取统计数据失败: ' + result.message, 'error');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                refreshData() {
                    this.loadStatistics();
                },

                applyDateFilter() {
                    if (!this.dateRange.start || !this.dateRange.end) {
                        showMessage('请选择完整的日期范围', 'warning');
                        return;
                    }

                    if (new Date(this.dateRange.start) > new Date(this.dateRange.end)) {
                        showMessage('开始日期不能大于结束日期', 'warning');
                        return;
                    }

                    this.loadStatistics();
                },

                formatCurrency(amount) {
                    return '¥' + (amount || 0).toLocaleString('zh-CN', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                },

                updateCharts() {
                    this.$nextTick(() => {
                        this.createUserChart();
                        this.createBookChart();
                        this.createOrderChart();
                        this.createActivityChart();
                        this.createRequestChart();
                    });
                },

                createUserChart() {
                    const ctx = document.getElementById('userChart');
                    if (!ctx) return;

                    if (this.charts.userChart) {
                        this.charts.userChart.destroy();
                    }

                    const roleData = this.stats.user_stats?.role_distribution || {};

                    this.charts.userChart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['教师', '出版社', '经销商', '管理员'],
                            datasets: [{
                                data: [
                                    roleData.teacher || 0,
                                    roleData.publisher || 0,
                                    roleData.dealer || 0,
                                    roleData.admin || 0
                                ],
                                backgroundColor: [
                                    '#3b82f6', // 蓝色
                                    '#10b981', // 绿色
                                    '#f59e0b', // 黄色
                                    '#8b5cf6'  // 紫色
                                ],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        boxWidth: 12,
                                        padding: 15,
                                        font: {
                                            size: 12
                                        }
                                    }
                                }
                            }
                        }
                    });
                },

                createBookChart() {
                    const ctx = document.getElementById('bookChart');
                    if (!ctx) return;

                    if (this.charts.bookChart) {
                        this.charts.bookChart.destroy();
                    }

                    const regulationData = this.stats.book_stats?.regulation_distribution || {};

                    this.charts.bookChart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['国家规划', '省级规划', '一般教材'],
                            datasets: [{
                                data: [
                                    regulationData.national || 0,
                                    regulationData.provincial || 0,
                                    regulationData.general || 0
                                ],
                                backgroundColor: [
                                    '#ef4444', // 红色
                                    '#3b82f6', // 蓝色
                                    '#6b7280'  // 灰色
                                ],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        boxWidth: 12,
                                        padding: 15,
                                        font: {
                                            size: 12
                                        }
                                    }
                                }
                            }
                        }
                    });
                },

                createOrderChart() {
                    const ctx = document.getElementById('orderChart');
                    if (!ctx) return;

                    if (this.charts.orderChart) {
                        this.charts.orderChart.destroy();
                    }

                    const statusData = this.stats.order_stats?.status_distribution || {};

                    this.charts.orderChart = new Chart(ctx, {
                        type: 'pie',
                        data: {
                            labels: ['预结算', '待支付', '已结算'],
                            datasets: [{
                                data: [
                                    statusData.pre_settlement || 0,
                                    statusData.pending_payment || 0,
                                    statusData.settled || 0
                                ],
                                backgroundColor: [
                                    '#f59e0b', // 黄色
                                    '#f97316', // 橙色
                                    '#10b981'  // 绿色
                                ],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        boxWidth: 12,
                                        padding: 15,
                                        font: {
                                            size: 12
                                        }
                                    }
                                }
                            }
                        }
                    });
                },

                createActivityChart() {
                    const ctx = document.getElementById('activityChart');
                    if (!ctx) return;

                    if (this.charts.activityChart) {
                        this.charts.activityChart.destroy();
                    }

                    const dailyActivity = this.stats.activity_stats?.daily_activity || [];

                    this.charts.activityChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: dailyActivity.map(item => item.date),
                            datasets: [{
                                label: '活跃用户数',
                                data: dailyActivity.map(item => item.active_users),
                                borderColor: '#6366f1',
                                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                },

                createRequestChart() {
                    const ctx = document.getElementById('requestChart');
                    if (!ctx) return;

                    if (this.charts.requestChart) {
                        this.charts.requestChart.destroy();
                    }

                    const statusData = this.stats.request_stats?.status_distribution || {};

                    this.charts.requestChart = new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['待处理', '已通过', '已拒绝'],
                            datasets: [{
                                label: '申请数量',
                                data: [
                                    statusData.pending || 0,
                                    statusData.approved || 0,
                                    statusData.rejected || 0
                                ],
                                backgroundColor: [
                                    '#f59e0b', // 黄色
                                    '#10b981', // 绿色
                                    '#ef4444'  // 红色
                                ],
                                borderRadius: 8,
                                borderSkipped: false,
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                }
            }
        }
    </script>
</body>
</html> 