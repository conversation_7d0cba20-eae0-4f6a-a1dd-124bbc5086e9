<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员首页 - 数据统计</title>
    <link rel="stylesheet" href="/static/css/tailwind.css">
    <link rel="stylesheet" href="/static/css/font-awesome-all.min.css">
    <script defer src="/static/js/alpine.min.js"></script>
    <script src="/static/jquery.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        /* 现代化设计样式 */
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .btn-success:hover {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
        }

        .btn-purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }
        .btn-purple:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 1rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .chart-container {
            height: 300px;
            position: relative;
        }

        .loading-skeleton {
            background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* 自定义滚动条 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* 数字动画 */
        .counter {
            font-variant-numeric: tabular-nums;
        }

        /* 响应式优化 */
        @media (max-width: 768px) {
            .chart-container {
                height: 250px;
            }
        }
    </style>
</head>
<<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen" x-data="dashboardManager()">
    <!-- 消息通知容器 -->
    <div id="messageContainer" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <div class="container mx-auto px-6 py-8">
        <!-- 页面头部 -->
        <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-slate-800 mb-2">管理员首页</h1>
                <p class="text-slate-600">系统数据统计与监控</p>
            </div>

            <!-- 日期选择和操作按钮 -->
            <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4 mt-4 lg:mt-0">
                <!-- 日期选择器 -->
                <div class="flex items-center space-x-2 bg-white/80 backdrop-blur-sm rounded-xl px-4 py-2 border border-slate-200">
                    <i class="fas fa-calendar-alt text-slate-500"></i>
                    <input type="date" x-model="dateRange.start" class="border-0 bg-transparent text-sm focus:outline-none">
                    <span class="text-slate-400">至</span>
                    <input type="date" x-model="dateRange.end" class="border-0 bg-transparent text-sm focus:outline-none">
                    <button @click="applyDateFilter()" class="btn-primary text-white px-3 py-1 rounded-lg text-sm">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <!-- 刷新按钮 -->
                <button @click="refreshData()"
                        class="btn-primary text-white px-6 py-3 rounded-xl flex items-center space-x-2 shadow-lg">
                    <i class="fas fa-sync-alt" :class="{'animate-spin': loading}"></i>
                    <span>刷新数据</span>
                </button>
            </div>
        </div>

        <!-- 核心统计卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- 用户统计 -->
            <div class="stat-card p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-blue-100 p-3 rounded-xl">
                        <i class="fas fa-users text-blue-600 text-2xl"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-slate-800 counter" x-text="stats.user_stats?.total_users || 0"></div>
                        <div class="text-sm text-slate-500">总用户数</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-sm">
                    <span class="text-slate-600">新增用户</span>
                    <span class="text-green-600 font-medium" x-text="'+' + (stats.user_stats?.new_users || 0)"></span>
                </div>
            </div>

            <!-- 样书统计 -->
            <div class="stat-card p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-green-100 p-3 rounded-xl">
                        <i class="fas fa-book text-green-600 text-2xl"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-slate-800 counter" x-text="stats.book_stats?.total_books || 0"></div>
                        <div class="text-sm text-slate-500">样书总数</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-sm">
                    <span class="text-slate-600">新增样书</span>
                    <span class="text-green-600 font-medium" x-text="'+' + (stats.book_stats?.new_books || 0)"></span>
                </div>
            </div>

            <!-- 订单统计 -->
            <div class="stat-card p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-purple-100 p-3 rounded-xl">
                        <i class="fas fa-shopping-cart text-purple-600 text-2xl"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-slate-800 counter" x-text="stats.order_stats?.total_orders || 0"></div>
                        <div class="text-sm text-slate-500">订单总数</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-sm">
                    <span class="text-slate-600">新增订单</span>
                    <span class="text-green-600 font-medium" x-text="'+' + (stats.order_stats?.new_orders || 0)"></span>
                </div>
            </div>

            <!-- 财务统计 -->
            <div class="stat-card p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="bg-yellow-100 p-3 rounded-xl">
                        <i class="fas fa-dollar-sign text-yellow-600 text-2xl"></i>
                    </div>
                    <div class="text-right">
                        <div class="text-2xl font-bold text-slate-800 counter" x-text="formatCurrency(stats.financial_stats?.total_revenue || 0)"></div>
                        <div class="text-sm text-slate-500">总交易额</div>
                    </div>
                </div>
                <div class="flex items-center justify-between text-sm">
                    <span class="text-slate-600">本期交易</span>
                    <span class="text-green-600 font-medium" x-text="formatCurrency(stats.financial_stats?.period_revenue || 0)"></span>
                </div>
            </div>
        </div>

        <!-- 详细统计图表区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
            <!-- 用户分布图表 -->
            <div class="stat-card overflow-hidden">
                <div class="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
                    <h3 class="text-lg font-semibold text-blue-700 flex items-center">
                        <i class="fas fa-users mr-2"></i>
                        用户分布统计
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-blue-600" x-text="stats.user_stats?.role_distribution?.teacher || 0"></div>
                            <div class="text-sm text-slate-500">教师用户</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600" x-text="stats.user_stats?.role_distribution?.publisher || 0"></div>
                            <div class="text-sm text-slate-500">出版社</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-yellow-600" x-text="stats.user_stats?.role_distribution?.dealer || 0"></div>
                            <div class="text-sm text-slate-500">经销商</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600" x-text="stats.user_stats?.role_distribution?.admin || 0"></div>
                            <div class="text-sm text-slate-500">管理员</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="userChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 订单状态统计 -->
            <div class="stat-card overflow-hidden">
                <div class="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-green-100">
                    <h3 class="text-lg font-semibold text-green-700 flex items-center">
                        <i class="fas fa-chart-bar mr-2"></i>
                        订单状态统计
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4 mb-6">
                        <div class="flex justify-between items-center">
                            <span class="text-slate-600">预结算</span>
                            <span class="font-semibold text-yellow-600" x-text="stats.order_stats?.status_distribution?.pre_settlement || 0"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-slate-600">待支付</span>
                            <span class="font-semibold text-orange-600" x-text="stats.order_stats?.status_distribution?.pending_payment || 0"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-slate-600">已结算</span>
                            <span class="font-semibold text-green-600" x-text="stats.order_stats?.status_distribution?.settled || 0"></span>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="orderChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 推广统计 -->
            <div class="stat-card overflow-hidden">
                <div class="p-6 bg-gradient-to-r from-purple-50 to-violet-50 border-b border-purple-100">
                    <h3 class="text-lg font-semibold text-purple-700 flex items-center">
                        <i class="fas fa-bullhorn mr-2"></i>
                        推广统计
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-purple-600" x-text="stats.promotion_stats?.total_reports || 0"></div>
                            <div class="text-sm text-slate-500">总推广数</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600" x-text="(stats.promotion_stats?.success_rate || 0) + '%'"></div>
                            <div class="text-sm text-slate-500">成功率</div>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-slate-600">待审核</span>
                            <span class="font-semibold text-yellow-600" x-text="stats.promotion_stats?.status_distribution?.pending || 0"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-slate-600">已通过</span>
                            <span class="font-semibold text-green-600" x-text="stats.promotion_stats?.status_distribution?.approved || 0"></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-slate-600">已拒绝</span>
                            <span class="font-semibold text-red-600" x-text="stats.promotion_stats?.status_distribution?.rejected || 0"></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 活跃度和地域分布 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- 系统活跃度 -->
            <div class="stat-card overflow-hidden">
                <div class="p-6 bg-gradient-to-r from-indigo-50 to-blue-50 border-b border-indigo-100">
                    <h3 class="text-lg font-semibold text-indigo-700 flex items-center">
                        <i class="fas fa-chart-line mr-2"></i>
                        系统活跃度
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-indigo-600" x-text="stats.activity_stats?.total_logs || 0"></div>
                            <div class="text-sm text-slate-500">总操作数</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-green-600" x-text="stats.user_stats?.active_users || 0"></div>
                            <div class="text-sm text-slate-500">活跃用户</div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="activityChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- 地域分布 -->
            <div class="stat-card overflow-hidden">
                <div class="p-6 bg-gradient-to-r from-emerald-50 to-teal-50 border-b border-emerald-100">
                    <h3 class="text-lg font-semibold text-emerald-700 flex items-center">
                        <i class="fas fa-map-marked-alt mr-2"></i>
                        地域分布
                    </h3>
                </div>
                <div class="p-6">
                    <div class="space-y-4 max-h-80 overflow-y-auto custom-scrollbar">
                        <template x-for="(item, index) in (stats.regional_stats?.school_distribution || [])" :key="index">
                            <div class="flex justify-between items-center p-3 bg-slate-50 rounded-lg">
                                <span class="text-slate-700" x-text="item.province"></span>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-slate-500">学校</span>
                                    <span class="font-semibold text-emerald-600" x-text="item.school_count"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷操作和最近活动 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 快捷操作 -->
            <div class="lg:col-span-2 stat-card overflow-hidden">
                <div class="p-6 bg-gradient-to-r from-slate-50 to-gray-50 border-b border-slate-200">
                    <h3 class="text-lg font-semibold text-slate-700 flex items-center">
                        <i class="fas fa-rocket mr-2"></i>
                        快捷操作
                    </h3>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <a href="/pc_admin_manage_users" class="group flex flex-col items-center p-4 bg-blue-50 rounded-xl hover:bg-blue-100 transition-all duration-300 hover:scale-105">
                            <div class="bg-blue-500 p-3 rounded-xl mb-3 group-hover:bg-blue-600 transition-colors">
                                <i class="fas fa-users text-white text-xl"></i>
                            </div>
                            <span class="text-blue-700 font-medium text-center">用户管理</span>
                        </a>
                        <a href="/admin_manage_samples" class="group flex flex-col items-center p-4 bg-green-50 rounded-xl hover:bg-green-100 transition-all duration-300 hover:scale-105">
                            <div class="bg-green-500 p-3 rounded-xl mb-3 group-hover:bg-green-600 transition-colors">
                                <i class="fas fa-book text-white text-xl"></i>
                            </div>
                            <span class="text-green-700 font-medium text-center">样书管理</span>
                        </a>
                        <a href="/pc_admin_manage_sample_requests" class="group flex flex-col items-center p-4 bg-yellow-50 rounded-xl hover:bg-yellow-100 transition-all duration-300 hover:scale-105">
                            <div class="bg-yellow-500 p-3 rounded-xl mb-3 group-hover:bg-yellow-600 transition-colors">
                                <i class="fas fa-file-alt text-white text-xl"></i>
                            </div>
                            <span class="text-yellow-700 font-medium text-center">样书申请</span>
                        </a>
                        <a href="/pc_admin_manage_exhibitions" class="group flex flex-col items-center p-4 bg-purple-50 rounded-xl hover:bg-purple-100 transition-all duration-300 hover:scale-105">
                            <div class="bg-purple-500 p-3 rounded-xl mb-3 group-hover:bg-purple-600 transition-colors">
                                <i class="fas fa-store text-white text-xl"></i>
                            </div>
                            <span class="text-purple-700 font-medium text-center">书展管理</span>
                        </a>
                        <a href="/pc_admin_manage_book_recommendations" class="group flex flex-col items-center p-4 bg-indigo-50 rounded-xl hover:bg-indigo-100 transition-all duration-300 hover:scale-105">
                            <div class="bg-indigo-500 p-3 rounded-xl mb-3 group-hover:bg-indigo-600 transition-colors">
                                <i class="fas fa-thumbs-up text-white text-xl"></i>
                            </div>
                            <span class="text-indigo-700 font-medium text-center">推荐管理</span>
                        </a>
                        <a href="/pc_admin_manage_audit_logs" class="group flex flex-col items-center p-4 bg-red-50 rounded-xl hover:bg-red-100 transition-all duration-300 hover:scale-105">
                            <div class="bg-red-500 p-3 rounded-xl mb-3 group-hover:bg-red-600 transition-colors">
                                <i class="fas fa-history text-white text-xl"></i>
                            </div>
                            <span class="text-red-700 font-medium text-center">操作日志</span>
                        </a>
                        <a href="/pc_admin_site_settings" class="group flex flex-col items-center p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-all duration-300 hover:scale-105">
                            <div class="bg-gray-500 p-3 rounded-xl mb-3 group-hover:bg-gray-600 transition-colors">
                                <i class="fas fa-cog text-white text-xl"></i>
                            </div>
                            <span class="text-gray-700 font-medium text-center">系统设置</span>
                        </a>
                        <a href="/admin_price_changes" class="group flex flex-col items-center p-4 bg-orange-50 rounded-xl hover:bg-orange-100 transition-all duration-300 hover:scale-105">
                            <div class="bg-orange-500 p-3 rounded-xl mb-3 group-hover:bg-orange-600 transition-colors">
                                <i class="fas fa-tags text-white text-xl"></i>
                            </div>
                            <span class="text-orange-700 font-medium text-center">价格管理</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="stat-card overflow-hidden">
                <div class="p-6 bg-gradient-to-r from-rose-50 to-pink-50 border-b border-rose-100">
                    <h3 class="text-lg font-semibold text-rose-700 flex items-center">
                        <i class="fas fa-bell mr-2"></i>
                        最近活动
                    </h3>
                </div>
                <div class="p-4">
                    <div class="space-y-3 max-h-96 overflow-y-auto custom-scrollbar">
                        <template x-for="(activity, index) in (stats.activity_stats?.action_distribution || [])" :key="index">
                            <div class="flex items-center p-3 bg-slate-50 rounded-lg hover:bg-slate-100 transition-colors">
                                <div class="bg-blue-100 p-2 rounded-lg mr-3">
                                    <i class="fas fa-activity text-blue-600"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-slate-800" x-text="activity.action_type"></div>
                                    <div class="text-xs text-slate-500" x-text="activity.count + ' 次操作'"></div>
                                </div>
                            </div>
                        </template>
                        <div x-show="!stats.activity_stats?.action_distribution?.length" class="text-center text-slate-500 py-8">
                            <i class="fas fa-inbox text-3xl mb-2"></i>
                            <p>暂无活动记录</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息通知组件 -->
    <script>
        let messageId = 0;

        function showMessage(text, type = 'info') {
            const id = ++messageId;
            const container = document.getElementById('messageContainer');

            const messageEl = document.createElement('div');
            messageEl.id = `message-${id}`;
            messageEl.className = `max-w-sm w-full bg-white border-l-4 rounded-lg shadow-lg p-4 transform transition-all duration-300 translate-x-full opacity-0 ${
                type === 'success' ? 'border-green-500' :
                type === 'error' ? 'border-red-500' :
                type === 'warning' ? 'border-yellow-500' : 'border-blue-500'
            }`;

            messageEl.innerHTML = `
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-5 h-5 mr-3 ${
                        type === 'success' ? 'text-green-500' :
                        type === 'error' ? 'text-red-500' :
                        type === 'warning' ? 'text-yellow-500' : 'text-blue-500'
                    }">
                        <i class="fas ${
                            type === 'success' ? 'fa-check-circle' :
                            type === 'error' ? 'fa-exclamation-circle' :
                            type === 'warning' ? 'fa-exclamation-triangle' : 'fa-info-circle'
                        }"></i>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm font-medium text-slate-800">${text}</p>
                    </div>
                    <button onclick="removeMessage(${id})"
                            class="flex-shrink-0 ml-3 text-slate-400 hover:text-slate-600">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            `;

            container.appendChild(messageEl);

            // 动画显示
            setTimeout(() => {
                messageEl.classList.remove('translate-x-full', 'opacity-0');
            }, 10);

            // 5秒后自动移除
            setTimeout(() => {
                removeMessage(id);
            }, 5000);
        }

        function removeMessage(id) {
            const messageEl = document.getElementById(`message-${id}`);
            if (messageEl) {
                messageEl.classList.add('translate-x-full', 'opacity-0');
                setTimeout(() => {
                    messageEl.remove();
                }, 300);
            }
        }
    </script>

    <script>
        function dashboardManager() {
            return {
                loading: false,
                stats: {},
                charts: {},
                dateRange: {
                    start: '',
                    end: ''
                },

                init() {
                    // 设置默认日期范围（最近30天）
                    const today = new Date();
                    const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

                    this.dateRange.end = today.toISOString().split('T')[0];
                    this.dateRange.start = thirtyDaysAgo.toISOString().split('T')[0];

                    this.loadStatistics();
                },

                async loadStatistics() {
                    this.loading = true;
                    try {
                        const params = new URLSearchParams({
                            start_date: this.dateRange.start,
                            end_date: this.dateRange.end
                        });

                        const response = await fetch(`/api/admin_dashboard/get_dashboard_stats?${params}`);
                        const result = await response.json();

                        if (result.code === 0) {
                            this.stats = result.data;
                            this.updateCharts();
                            showMessage('数据加载成功', 'success');
                        } else {
                            showMessage('获取统计数据失败: ' + result.message, 'error');
                        }
                    } catch (error) {
                        console.error('Error:', error);
                        showMessage('网络错误，请稍后重试', 'error');
                    } finally {
                        this.loading = false;
                    }
                },

                refreshData() {
                    this.loadStatistics();
                },

                applyDateFilter() {
                    if (!this.dateRange.start || !this.dateRange.end) {
                        showMessage('请选择完整的日期范围', 'warning');
                        return;
                    }

                    if (new Date(this.dateRange.start) > new Date(this.dateRange.end)) {
                        showMessage('开始日期不能大于结束日期', 'warning');
                        return;
                    }

                    this.loadStatistics();
                },

                formatCurrency(amount) {
                    return '¥' + (amount || 0).toLocaleString('zh-CN', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                },

                updateCharts() {
                    this.$nextTick(() => {
                        this.createUserChart();
                        this.createOrderChart();
                        this.createActivityChart();
                    });
                },

                createUserChart() {
                    const ctx = document.getElementById('userChart');
                    if (!ctx) return;

                    if (this.charts.userChart) {
                        this.charts.userChart.destroy();
                    }

                    const roleData = this.stats.user_stats?.role_distribution || {};

                    this.charts.userChart = new Chart(ctx, {
                        type: 'doughnut',
                        data: {
                            labels: ['教师', '出版社', '经销商', '管理员'],
                            datasets: [{
                                data: [
                                    roleData.teacher || 0,
                                    roleData.publisher || 0,
                                    roleData.dealer || 0,
                                    roleData.admin || 0
                                ],
                                backgroundColor: [
                                    '#3b82f6', // 蓝色
                                    '#10b981', // 绿色
                                    '#f59e0b', // 黄色
                                    '#8b5cf6'  // 紫色
                                ],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        boxWidth: 12,
                                        padding: 15,
                                        font: {
                                            size: 12
                                        }
                                    }
                                }
                            }
                        }
                    });
                },

                createOrderChart() {
                    const ctx = document.getElementById('orderChart');
                    if (!ctx) return;

                    if (this.charts.orderChart) {
                        this.charts.orderChart.destroy();
                    }

                    const statusData = this.stats.order_stats?.status_distribution || {};

                    this.charts.orderChart = new Chart(ctx, {
                        type: 'pie',
                        data: {
                            labels: ['预结算', '待支付', '已结算'],
                            datasets: [{
                                data: [
                                    statusData.pre_settlement || 0,
                                    statusData.pending_payment || 0,
                                    statusData.settled || 0
                                ],
                                backgroundColor: [
                                    '#f59e0b', // 黄色
                                    '#f97316', // 橙色
                                    '#10b981'  // 绿色
                                ],
                                borderWidth: 0
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    position: 'bottom',
                                    labels: {
                                        boxWidth: 12,
                                        padding: 15,
                                        font: {
                                            size: 12
                                        }
                                    }
                                }
                            }
                        }
                    });
                },

                createActivityChart() {
                    const ctx = document.getElementById('activityChart');
                    if (!ctx) return;

                    if (this.charts.activityChart) {
                        this.charts.activityChart.destroy();
                    }

                    const dailyActivity = this.stats.activity_stats?.daily_activity || [];

                    this.charts.activityChart = new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: dailyActivity.map(item => item.date),
                            datasets: [{
                                label: '活跃用户数',
                                data: dailyActivity.map(item => item.active_users),
                                borderColor: '#6366f1',
                                backgroundColor: 'rgba(99, 102, 241, 0.1)',
                                borderWidth: 2,
                                fill: true,
                                tension: 0.4
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    ticks: {
                                        precision: 0
                                    }
                                }
                            },
                            plugins: {
                                legend: {
                                    display: false
                                }
                            }
                        }
                    });
                }
            }
        }
    </script>
</body>
</html> 