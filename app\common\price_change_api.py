"""
样书价格费率变动API接口
提供价格变动记录的查询和管理功能
"""

from flask import Blueprint, request, jsonify, session
from app.common.price_change_service import PriceChangeService
import json

price_change_bp = Blueprint('price_change', __name__)


@price_change_bp.route('/api/admin/price_changes', methods=['GET'])
def get_price_changes():
    """获取价格变动记录列表 - 管理员专用"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        search = request.args.get('search', '')
        change_type = request.args.get('change_type', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        operator_id = request.args.get('operator_id', '')
        sample_book_id = request.args.get('sample_book_id', '')
        
        # 处理空字符串参数
        change_type = change_type if change_type else None
        start_date = start_date if start_date else None
        end_date = end_date if end_date else None
        operator_id = int(operator_id) if operator_id else None
        sample_book_id = int(sample_book_id) if sample_book_id else None
        search = search if search else None
        
        # 调用服务获取数据
        result = PriceChangeService.get_price_changes(
            sample_book_id=sample_book_id,
            change_type=change_type,
            operator_id=operator_id,
            start_date=start_date,
            end_date=end_date,
            page=page,
            limit=limit,
            search=search
        )
        
        if result is None:
            return jsonify({"code": 1, "message": "获取数据失败"})
        
        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": result['records'],
            "total": result['total'],
            "page": page,
            "limit": limit
        })
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取价格变动记录失败: {str(e)}"})


@price_change_bp.route('/api/admin/sample_change_history/<int:sample_book_id>', methods=['GET'])
def get_sample_change_history(sample_book_id):
    """获取指定样书的变动历史 - 管理员专用"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    try:
        records = PriceChangeService.get_sample_change_history(sample_book_id)
        
        return jsonify({
            "code": 0,
            "message": "获取成功",
            "data": records
        })
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取样书变动历史失败: {str(e)}"})


@price_change_bp.route('/api/admin/price_change_statistics', methods=['GET'])
def get_price_change_statistics():
    """获取价格变动统计信息 - 管理员专用"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    try:
        from app.config import get_db_connection
        
        connection = get_db_connection()
        try:
            with connection.cursor() as cursor:
                # 获取各类型变动统计
                type_stats_sql = """
                    SELECT change_type, COUNT(*) as count
                    FROM sample_book_price_changes
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                    GROUP BY change_type
                """
                cursor.execute(type_stats_sql)
                type_stats = cursor.fetchall()
                
                # 获取操作人统计
                operator_stats_sql = """
                    SELECT u.name as operator_name, pc.operator_type, COUNT(*) as count
                    FROM sample_book_price_changes pc
                    JOIN users u ON pc.operator_id = u.user_id
                    WHERE pc.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                    GROUP BY pc.operator_id, u.name, pc.operator_type
                    ORDER BY count DESC
                    LIMIT 10
                """
                cursor.execute(operator_stats_sql)
                operator_stats = cursor.fetchall()
                
                # 获取每日变动趋势（最近7天）
                daily_stats_sql = """
                    SELECT DATE(created_at) as date, COUNT(*) as count
                    FROM sample_book_price_changes
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                    GROUP BY DATE(created_at)
                    ORDER BY date
                """
                cursor.execute(daily_stats_sql)
                daily_stats = cursor.fetchall()
                
                # 格式化数据
                type_map = {
                    'price': '价格',
                    'shipping_discount': '发货折扣',
                    'settlement_discount': '结算折扣',
                    'promotion_rate': '推广费率'
                }
                
                for stat in type_stats:
                    stat['change_type_name'] = type_map.get(stat['change_type'], stat['change_type'])
                
                for stat in operator_stats:
                    stat['operator_type_name'] = '管理员' if stat['operator_type'] == 'admin' else '出版社'
                
                for stat in daily_stats:
                    stat['date'] = stat['date'].strftime('%Y-%m-%d')
                
                return jsonify({
                    "code": 0,
                    "message": "获取成功",
                    "data": {
                        "type_stats": type_stats,
                        "operator_stats": operator_stats,
                        "daily_stats": daily_stats
                    }
                })
        finally:
            connection.close()
            
    except Exception as e:
        return jsonify({"code": 1, "message": f"获取统计信息失败: {str(e)}"})


@price_change_bp.route('/api/admin/export_price_changes', methods=['GET'])
def export_price_changes():
    """导出价格变动记录 - 管理员专用"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({"code": 1, "message": "未授权访问"})
    
    try:
        import pandas as pd
        import tempfile
        from flask import send_file
        from datetime import datetime
        
        # 获取查询参数
        change_type = request.args.get('change_type', '')
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        operator_id = request.args.get('operator_id', '')
        search = request.args.get('search', '')
        
        # 处理参数
        change_type = change_type if change_type else None
        start_date = start_date if start_date else None
        end_date = end_date if end_date else None
        operator_id = int(operator_id) if operator_id else None
        search = search if search else None
        
        # 获取所有符合条件的记录
        result = PriceChangeService.get_price_changes(
            change_type=change_type,
            operator_id=operator_id,
            start_date=start_date,
            end_date=end_date,
            page=1,
            limit=10000,  # 大数量导出
            search=search
        )
        
        if not result or not result['records']:
            return jsonify({"code": 1, "message": "暂无数据可导出"})
        
        # 准备导出数据
        export_data = []
        for record in result['records']:
            export_data.append({
                '样书名称': record['book_name'],
                'ISBN': record['isbn'],
                '作者': record['author'],
                '变动类型': record['change_type_name'],
                '变动前': record['old_value_display'],
                '变动后': record['new_value_display'],
                '变动原因': record['change_reason'] or '',
                '操作人': record['operator_name'],
                '操作人类型': record['operator_type_name'],
                '变动时间': record['created_at'],
                'IP地址': record['ip_address'] or ''
            })
        
        # 创建Excel文件
        df = pd.DataFrame(export_data)
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx')
        
        with pd.ExcelWriter(temp_file.name, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='价格变动记录', index=False)
            
            # 设置列宽
            workbook = writer.book
            worksheet = writer.sheets['价格变动记录']
            
            column_widths = {
                '样书名称': 30,
                'ISBN': 15,
                '作者': 20,
                '变动类型': 12,
                '变动前': 15,
                '变动后': 15,
                '变动原因': 25,
                '操作人': 15,
                '操作人类型': 12,
                '变动时间': 20,
                'IP地址': 15
            }
            
            for i, col in enumerate(df.columns):
                width = column_widths.get(col, 15)
                worksheet.set_column(i, i, width)
            
            # 设置表头格式
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })
            
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
        
        # 生成文件名
        current_time = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"样书价格变动记录_{current_time}.xlsx"
        
        return send_file(
            temp_file.name,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        
    except Exception as e:
        return jsonify({"code": 1, "message": f"导出失败: {str(e)}"})


# 为了在样书修改时自动记录变动，需要提供一个辅助函数
def record_sample_book_changes(sample_book_id, old_data, new_data, operator_id=None, operator_type=None):
    """
    记录样书价格费率变动
    在样书更新时调用此函数来自动记录变动
    """
    try:
        from decimal import Decimal

        def safe_compare(old_val, new_val):
            """安全比较两个数值，处理None、Decimal、float等类型"""
            # 如果都是None，认为相等
            if old_val is None and new_val is None:
                return True

            # 如果一个是None另一个不是，认为不相等
            if old_val is None or new_val is None:
                return False

            # 转换为Decimal进行精确比较
            try:
                old_decimal = Decimal(str(old_val))
                new_decimal = Decimal(str(new_val))
                return old_decimal == new_decimal
            except:
                # 如果转换失败，直接比较
                return old_val == new_val

        # 检查价格变动
        old_price = old_data.get('price')
        new_price = new_data.get('price')
        if not safe_compare(old_price, new_price):
            PriceChangeService.record_price_change(
                sample_book_id=sample_book_id,
                change_type='price',
                old_value=float(old_price) if old_price is not None else None,
                new_value=float(new_price) if new_price is not None else None,
                operator_id=operator_id,
                operator_type=operator_type
            )

        # 检查发货折扣变动
        old_shipping = old_data.get('shipping_discount')
        new_shipping = new_data.get('shipping_discount')
        if not safe_compare(old_shipping, new_shipping):
            PriceChangeService.record_price_change(
                sample_book_id=sample_book_id,
                change_type='shipping_discount',
                old_value=float(old_shipping) if old_shipping is not None else None,
                new_value=float(new_shipping) if new_shipping is not None else None,
                operator_id=operator_id,
                operator_type=operator_type
            )

        # 检查结算折扣变动
        old_settlement = old_data.get('settlement_discount')
        new_settlement = new_data.get('settlement_discount')
        if not safe_compare(old_settlement, new_settlement):
            PriceChangeService.record_price_change(
                sample_book_id=sample_book_id,
                change_type='settlement_discount',
                old_value=float(old_settlement) if old_settlement is not None else None,
                new_value=float(new_settlement) if new_settlement is not None else None,
                operator_id=operator_id,
                operator_type=operator_type
            )

        # 检查推广费率变动
        old_promotion = old_data.get('promotion_rate')
        new_promotion = new_data.get('promotion_rate')
        if not safe_compare(old_promotion, new_promotion):
            PriceChangeService.record_price_change(
                sample_book_id=sample_book_id,
                change_type='promotion_rate',
                old_value=float(old_promotion) if old_promotion is not None else None,
                new_value=float(new_promotion) if new_promotion is not None else None,
                operator_id=operator_id,
                operator_type=operator_type
            )

    except Exception as e:
        print(f"记录样书变动失败: {str(e)}")
